"""
Sub-application registry for managing sub-application registration and discovery.

This module provides a centralized registry for sub-application management,
including registration, discovery, and lifecycle management.
"""

from typing import Dict, List, Optional, Any
import asyncio
from fastapi import FastAPI
from core.container import singleton
from core.log import logger
from .base import BaseSubApplication, SubApplicationConfig, SubApplicationMetricsCollector


@singleton()
class SubApplicationRegistry:
    """
    Central registry for sub-application management.
    
    Handles sub-application registration, discovery, and lifecycle management.
    """
    
    def __init__(self):
        self._sub_applications: Dict[str, BaseSubApplication] = {}
        self._mount_order: List[str] = []
        self._enabled_sub_applications: Dict[str, bool] = {}
        self._metrics_collector = SubApplicationMetricsCollector()
    
    def register(self, sub_application: BaseSubApplication) -> None:
        """
        Register a sub-application instance.
        
        Args:
            sub_application: Sub-application instance to register
        """
        name = sub_application.get_name()
        
        if name in self._sub_applications:
            logger.warning(f"Sub-application '{name}' is already registered, replacing...")
        
        # Validate configuration
        if not sub_application.validate_config():
            raise ValueError(f"Invalid configuration for sub-application '{name}'")
        
        # Check for mount path conflicts
        mount_path = sub_application.get_mount_path()
        for existing_name, existing_sub_app in self._sub_applications.items():
            if (existing_sub_app.get_mount_path() == mount_path and 
                existing_name != name and 
                self._enabled_sub_applications.get(existing_name, False)):
                raise ValueError(
                    f"Mount path conflict: '{mount_path}' is already used by '{existing_name}'"
                )
        
        self._sub_applications[name] = sub_application
        self._enabled_sub_applications[name] = sub_application.is_enabled()
        
        # Update order based on priority
        self._update_mount_order()
        
        logger.info(f"Registered sub-application: {sub_application}")
    
    def unregister(self, name: str) -> bool:
        """
        Unregister a sub-application.
        
        Args:
            name: Name of the sub-application to unregister
            
        Returns:
            True if sub-application was unregistered, False if not found
        """
        if name not in self._sub_applications:
            return False
        
        sub_application = self._sub_applications[name]
        
        # Cleanup sub-application
        asyncio.create_task(sub_application.cleanup())
        
        # Remove from registry
        del self._sub_applications[name]
        del self._enabled_sub_applications[name]
        
        # Remove from order
        if name in self._mount_order:
            self._mount_order.remove(name)
        
        logger.info(f"Unregistered sub-application: {name}")
        return True
    
    def get_sub_application(self, name: str) -> Optional[BaseSubApplication]:
        """Get a sub-application by name."""
        return self._sub_applications.get(name)
    
    def get_all_sub_applications(self) -> Dict[str, BaseSubApplication]:
        """Get all registered sub-applications."""
        return self._sub_applications.copy()
    
    def get_enabled_sub_applications(self) -> Dict[str, BaseSubApplication]:
        """Get all enabled sub-applications."""
        return {
            name: sub_app 
            for name, sub_app in self._sub_applications.items()
            if self._enabled_sub_applications.get(name, False)
        }
    
    def get_mount_order(self) -> List[str]:
        """Get the sub-application mount order."""
        return [
            name for name in self._mount_order 
            if self._enabled_sub_applications.get(name, False)
        ]
    
    def enable_sub_application(self, name: str) -> bool:
        """Enable a sub-application."""
        if name not in self._sub_applications:
            return False
        
        self._enabled_sub_applications[name] = True
        self._sub_applications[name].update_config(enabled=True)
        logger.info(f"Enabled sub-application: {name}")
        return True
    
    def disable_sub_application(self, name: str) -> bool:
        """Disable a sub-application."""
        if name not in self._sub_applications:
            return False
        
        self._enabled_sub_applications[name] = False
        self._sub_applications[name].update_config(enabled=False)
        logger.info(f"Disabled sub-application: {name}")
        return True
    
    def is_registered(self, name: str) -> bool:
        """Check if a sub-application is registered."""
        return name in self._sub_applications
    
    def is_enabled(self, name: str) -> bool:
        """Check if a sub-application is enabled."""
        return self._enabled_sub_applications.get(name, False)
    
    async def mount_all(self, app: FastAPI) -> None:
        """Mount all enabled sub-applications on the FastAPI application."""
        enabled_sub_applications = self.get_enabled_sub_applications()
        mount_order = self.get_mount_order()
        
        logger.info(f"Mounting {len(enabled_sub_applications)} sub-applications...")
        
        for name in mount_order:
            if name in enabled_sub_applications:
                sub_application = enabled_sub_applications[name]
                
                try:
                    import time
                    start_time = time.time()
                    
                    await sub_application.mount(app)
                    
                    mount_time = time.time() - start_time
                    self._metrics_collector.get_metrics().record_mount_time(name, mount_time)
                    
                    logger.info(f"Mounted sub-application '{name}' at '{sub_application.get_mount_path()}' in {mount_time:.3f}s")
                    
                except Exception as e:
                    logger.error(f"Failed to mount sub-application '{name}': {e}")
                    raise
        
        logger.info("All sub-applications mounted successfully")
    
    def get_sub_application_info(self) -> Dict[str, Any]:
        """Get information about all registered sub-applications."""
        info = {}
        
        for name, sub_app in self._sub_applications.items():
            info[name] = {
                "name": sub_app.get_name(),
                "description": sub_app.get_description(),
                "enabled": self.is_enabled(name),
                "mounted": sub_app.is_mounted(),
                "mount_path": sub_app.get_mount_path(),
                "priority": sub_app.get_priority(),
                "config": sub_app.get_config_dict(),
                "health_status": sub_app.get_health_status()
            }
        
        return info
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get sub-application metrics."""
        return self._metrics_collector.get_metrics().get_metrics()
    
    def get_mount_paths(self) -> Dict[str, str]:
        """Get mapping of sub-application names to mount paths."""
        return {
            name: sub_app.get_mount_path()
            for name, sub_app in self._sub_applications.items()
            if self.is_enabled(name)
        }
    
    def find_by_mount_path(self, mount_path: str) -> Optional[BaseSubApplication]:
        """Find a sub-application by its mount path."""
        for sub_app in self._sub_applications.values():
            if sub_app.get_mount_path() == mount_path:
                return sub_app
        return None
    
    def _update_mount_order(self) -> None:
        """Update mount order based on priority."""
        # Sort by priority (lower number = higher priority)
        sorted_sub_applications = sorted(
            self._sub_applications.items(),
            key=lambda x: x[1].get_priority()
        )
        
        self._mount_order = [name for name, _ in sorted_sub_applications]
    
    def validate_mount_paths(self) -> List[str]:
        """
        Validate mount paths for conflicts.
        
        Returns:
            List of conflict descriptions
        """
        conflicts = []
        mount_paths = {}
        
        for name, sub_app in self._sub_applications.items():
            if not self.is_enabled(name):
                continue
            
            mount_path = sub_app.get_mount_path()
            
            if mount_path in mount_paths:
                conflicts.append(
                    f"Mount path conflict: '{mount_path}' used by both "
                    f"'{mount_paths[mount_path]}' and '{name}'"
                )
            else:
                mount_paths[mount_path] = name
        
        return conflicts
    
    def clear(self) -> None:
        """Clear all registered sub-applications."""
        self._sub_applications.clear()
        self._mount_order.clear()
        self._enabled_sub_applications.clear()
        logger.info("Cleared all sub-applications from registry")
