"""
Configuration factory for different environments.

This module provides a factory pattern for creating configuration
objects based on the current environment.
"""

import os
from functools import lru_cache
from typing import Type, TypeVar, Union
from .settings import (
    BaseConfig,
    AppSettings,
    DatabaseSettings,
    RedisSettings,
    JwtSettings,
    GenSettings,
    UploadSettings
)

T = TypeVar('T', bound=BaseConfig)


class ConfigFactory:
    """Factory for creating configuration objects."""
    
    _instances: dict[str, BaseConfig] = {}
    
    @classmethod
    def _get_env_file(cls, env: str) -> str:
        """Get environment file path based on environment."""
        env_files = {
            "dev": ".env.dev",
            "test": ".env.test", 
            "prod": ".env.prod",
            "local": ".env.local"
        }
        
        env_file = env_files.get(env, ".env")
        
        # Check if file exists, fallback to .env
        if not os.path.exists(env_file):
            env_file = ".env"
            
        return env_file
    
    @classmethod
    def create_config(cls, config_class: Type[T], env: str = None) -> T:
        """
        Create configuration instance for specific environment.
        
        Args:
            config_class: Configuration class to instantiate
            env: Environment name (dev, test, prod, local)
            
        Returns:
            Configuration instance
        """
        if env is None:
            env = os.getenv("APP_ENV", "dev")
            
        cache_key = f"{config_class.__name__}_{env}"
        
        if cache_key not in cls._instances:
            env_file = cls._get_env_file(env)
            
            # Create instance with specific env file
            instance = config_class(_env_file=env_file)
            cls._instances[cache_key] = instance
            
        return cls._instances[cache_key]
    
    @classmethod
    def get_app_config(cls, env: str = None) -> AppSettings:
        """Get application configuration."""
        return cls.create_config(AppSettings, env)
    
    @classmethod
    def get_database_config(cls, env: str = None) -> DatabaseSettings:
        """Get database configuration."""
        return cls.create_config(DatabaseSettings, env)
    
    @classmethod
    def get_redis_config(cls, env: str = None) -> RedisSettings:
        """Get Redis configuration."""
        return cls.create_config(RedisSettings, env)
    
    @classmethod
    def get_jwt_config(cls, env: str = None) -> JwtSettings:
        """Get JWT configuration."""
        return cls.create_config(JwtSettings, env)
    
    @classmethod
    def get_gen_config(cls, env: str = None) -> GenSettings:
        """Get code generation configuration."""
        return cls.create_config(GenSettings, env)
    
    @classmethod
    def get_upload_config(cls, env: str = None) -> UploadSettings:
        """Get upload configuration."""
        return cls.create_config(UploadSettings, env)
    
    @classmethod
    def get_all_configs(cls, env: str = None) -> dict[str, BaseConfig]:
        """
        Get all configuration objects.
        
        Args:
            env: Environment name
            
        Returns:
            Dictionary of all configuration objects
        """
        return {
            "app": cls.get_app_config(env),
            "database": cls.get_database_config(env),
            "redis": cls.get_redis_config(env),
            "jwt": cls.get_jwt_config(env),
            "gen": cls.get_gen_config(env),
            "upload": cls.get_upload_config(env)
        }
    
    @classmethod
    def clear_cache(cls):
        """Clear configuration cache."""
        cls._instances.clear()
    
    @classmethod
    def reload_config(cls, config_class: Type[T], env: str = None) -> T:
        """
        Reload configuration by clearing cache and creating new instance.
        
        Args:
            config_class: Configuration class to reload
            env: Environment name
            
        Returns:
            New configuration instance
        """
        if env is None:
            env = os.getenv("APP_ENV", "dev")
            
        cache_key = f"{config_class.__name__}_{env}"
        
        # Remove from cache if exists
        if cache_key in cls._instances:
            del cls._instances[cache_key]
            
        # Create new instance
        return cls.create_config(config_class, env)


# Convenience functions for getting configurations
@lru_cache()
def get_app_config() -> AppSettings:
    """Get cached application configuration."""
    return ConfigFactory.get_app_config()


@lru_cache()
def get_database_config() -> DatabaseSettings:
    """Get cached database configuration."""
    return ConfigFactory.get_database_config()


@lru_cache()
def get_redis_config() -> RedisSettings:
    """Get cached Redis configuration."""
    return ConfigFactory.get_redis_config()


@lru_cache()
def get_jwt_config() -> JwtSettings:
    """Get cached JWT configuration."""
    return ConfigFactory.get_jwt_config()


@lru_cache()
def get_gen_config() -> GenSettings:
    """Get cached code generation configuration."""
    return ConfigFactory.get_gen_config()


@lru_cache()
def get_upload_config() -> UploadSettings:
    """Get cached upload configuration."""
    return ConfigFactory.get_upload_config()


# Global configuration instances (backward compatibility)
AppConfig = get_app_config()
DataBaseConfig = get_database_config()
RedisConfig = get_redis_config()
JwtConfig = get_jwt_config()
GenConfig = get_gen_config()
UploadConfig = get_upload_config()
