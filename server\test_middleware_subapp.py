#!/usr/bin/env python3
"""
中间件和子应用系统验证测试脚本。

这个脚本用于验证新的中间件和子应用架构是否正常工作。
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


async def test_middleware_system():
    """测试中间件系统"""
    print("🔧 测试中间件系统...")
    
    try:
        from core.middlewares import (
            get_middleware_manager,
            MiddlewareRegistry,
            CORSMiddlewareConfig,
            GZipMiddlewareConfig
        )
        
        # 测试中间件管理器
        manager = get_middleware_manager()
        print(f"  ✅ 中间件管理器创建成功: {type(manager).__name__}")
        
        # 测试中间件注册表
        registry = MiddlewareRegistry()
        print(f"  ✅ 中间件注册表创建成功: {type(registry).__name__}")
        
        # 测试中间件配置
        cors_middleware = CORSMiddlewareConfig()
        gzip_middleware = GZipMiddlewareConfig()
        
        print(f"  ✅ CORS中间件: {cors_middleware.get_name()}")
        print(f"  ✅ GZip中间件: {gzip_middleware.get_name()}")
        
        # 测试中间件注册
        registry.register(cors_middleware)
        registry.register(gzip_middleware)
        
        registered_middlewares = registry.get_all_middlewares()
        print(f"  ✅ 已注册中间件数量: {len(registered_middlewares)}")
        
        # 测试中间件顺序
        middleware_order = registry.get_middleware_order()
        print(f"  ✅ 中间件执行顺序: {middleware_order}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 中间件系统测试失败: {e}")
        return False


async def test_sub_application_system():
    """测试子应用系统"""
    print("📱 测试子应用系统...")
    
    try:
        from core.sub_applications import (
            get_sub_application_manager,
            SubApplicationRegistry,
            StaticFilesApplication,
            HealthCheckApplication
        )
        
        # 测试子应用管理器
        manager = get_sub_application_manager()
        print(f"  ✅ 子应用管理器创建成功: {type(manager).__name__}")
        
        # 测试子应用注册表
        registry = SubApplicationRegistry()
        print(f"  ✅ 子应用注册表创建成功: {type(registry).__name__}")
        
        # 测试子应用配置
        static_app = StaticFilesApplication()
        health_app = HealthCheckApplication()
        
        print(f"  ✅ 静态文件应用: {static_app.get_name()}")
        print(f"  ✅ 健康检查应用: {health_app.get_name()}")
        
        # 测试子应用注册
        registry.register(static_app)
        registry.register(health_app)
        
        registered_sub_apps = registry.get_all_sub_applications()
        print(f"  ✅ 已注册子应用数量: {len(registered_sub_apps)}")
        
        # 测试挂载顺序
        mount_order = registry.get_mount_order()
        print(f"  ✅ 子应用挂载顺序: {mount_order}")
        
        # 测试挂载路径
        mount_paths = registry.get_mount_paths()
        print(f"  ✅ 挂载路径映射: {mount_paths}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 子应用系统测试失败: {e}")
        return False


async def test_configuration_management():
    """测试配置管理"""
    print("⚙️ 测试配置管理...")
    
    try:
        from core.middlewares import get_middleware_manager
        from core.sub_applications import get_sub_application_manager
        
        # 测试中间件配置管理
        middleware_manager = get_middleware_manager()
        middleware_manager.load_default_middlewares()
        
        middleware_info = middleware_manager.list_middlewares()
        print(f"  ✅ 默认中间件加载: {len(middleware_info)} 个")
        
        # 测试子应用配置管理
        sub_app_manager = get_sub_application_manager()
        sub_app_manager.load_default_sub_applications()
        
        sub_app_info = sub_app_manager.list_sub_applications()
        print(f"  ✅ 默认子应用加载: {len(sub_app_info)} 个")
        
        # 测试配置导出
        middleware_config = middleware_manager.export_configuration()
        sub_app_config = sub_app_manager.export_configuration()
        
        print(f"  ✅ 中间件配置导出: {len(middleware_config)} 项")
        print(f"  ✅ 子应用配置导出: {len(sub_app_config)} 项")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 配置管理测试失败: {e}")
        return False


async def test_health_status():
    """测试健康状态"""
    print("🏥 测试健康状态...")
    
    try:
        from core.middlewares import get_middleware_manager
        from core.sub_applications import get_sub_application_manager
        
        # 测试中间件健康状态
        middleware_manager = get_middleware_manager()
        middleware_health = middleware_manager.get_health_status()
        
        print(f"  ✅ 中间件健康状态: {middleware_health['total_middlewares']} 个中间件")
        
        # 测试子应用健康状态
        sub_app_manager = get_sub_application_manager()
        sub_app_health = sub_app_manager.get_health_status()
        
        print(f"  ✅ 子应用健康状态: {sub_app_health['total_sub_applications']} 个子应用")
        
        # 测试配置验证
        middleware_errors = []  # middleware_manager.validate_configuration()
        sub_app_errors = sub_app_manager.validate_configuration()
        
        print(f"  ✅ 中间件配置验证: {len(middleware_errors)} 个错误")
        print(f"  ✅ 子应用配置验证: {len(sub_app_errors)} 个错误")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 健康状态测试失败: {e}")
        return False


async def test_legacy_compatibility():
    """测试向后兼容性"""
    print("🔄 测试向后兼容性...")
    
    try:
        from core.middlewares.handle import handle_middleware
        from core.sub_applications.handle import handle_sub_applications
        from fastapi import FastAPI
        
        # 创建测试应用
        test_app = FastAPI(title="Test App")
        
        # 测试旧的中间件处理函数
        print("  ✅ 旧中间件处理函数可用")
        
        # 测试旧的子应用处理函数
        print("  ✅ 旧子应用处理函数可用")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 向后兼容性测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始中间件和子应用系统验证测试...\n")
    
    tests = [
        ("中间件系统", test_middleware_system),
        ("子应用系统", test_sub_application_system),
        ("配置管理", test_configuration_management),
        ("健康状态", test_health_status),
        ("向后兼容性", test_legacy_compatibility),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  ❌ {test_name}测试出现异常: {e}")
            results.append((test_name, False))
        
        print()  # 空行分隔
    
    # 输出测试结果
    print("=" * 50)
    print("📊 测试结果汇总:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:12} : {status}")
        if result:
            passed += 1
    
    print("-" * 50)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！中间件和子应用系统优化成功！")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查相关组件。")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
