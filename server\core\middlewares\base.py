"""
Base middleware classes and interfaces.

This module provides the foundation for the middleware system,
including base classes and configuration interfaces.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, Type
from fastapi import FastAPI
from pydantic import BaseModel, Field
from core.container import singleton


class MiddlewareConfig(BaseModel):
    """Base configuration class for middleware."""
    
    enabled: bool = Field(default=True, description="Whether the middleware is enabled")
    priority: int = Field(default=100, description="Middleware priority (lower = higher priority)")
    
    class Config:
        extra = "allow"  # Allow additional configuration fields


class BaseMiddleware(ABC):
    """
    Base class for all middleware implementations.
    
    Provides a common interface for middleware registration,
    configuration, and lifecycle management.
    """
    
    def __init__(self, config: MiddlewareConfig = None):
        self.config = config or MiddlewareConfig()
        self._initialized = False
    
    @abstractmethod
    def get_name(self) -> str:
        """Get the middleware name."""
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """Get the middleware description."""
        pass
    
    def get_priority(self) -> int:
        """Get the middleware priority."""
        return self.config.priority
    
    def is_enabled(self) -> bool:
        """Check if the middleware is enabled."""
        return self.config.enabled
    
    @abstractmethod
    async def setup(self, app: FastAPI) -> None:
        """
        Setup the middleware on the FastAPI application.
        
        Args:
            app: FastAPI application instance
        """
        pass
    
    async def initialize(self) -> None:
        """Initialize the middleware (called once during startup)."""
        if not self._initialized:
            await self._initialize()
            self._initialized = True
    
    async def _initialize(self) -> None:
        """Override this method for custom initialization logic."""
        pass
    
    async def cleanup(self) -> None:
        """Cleanup resources when the middleware is removed."""
        pass
    
    def validate_config(self) -> bool:
        """Validate the middleware configuration."""
        try:
            self.config.model_validate(self.config.model_dump())
            return True
        except Exception:
            return False
    
    def get_config_dict(self) -> Dict[str, Any]:
        """Get configuration as dictionary."""
        return self.config.model_dump()
    
    def update_config(self, **kwargs) -> None:
        """Update middleware configuration."""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
    
    def __str__(self) -> str:
        """String representation of the middleware."""
        return f"{self.get_name()}(enabled={self.is_enabled()}, priority={self.get_priority()})"
    
    def __repr__(self) -> str:
        """Detailed representation of the middleware."""
        return (
            f"{self.__class__.__name__}("
            f"name='{self.get_name()}', "
            f"enabled={self.is_enabled()}, "
            f"priority={self.get_priority()})"
        )


class MiddlewareFactory:
    """Factory for creating middleware instances."""
    
    _middleware_classes: Dict[str, Type[BaseMiddleware]] = {}
    
    @classmethod
    def register_middleware(cls, name: str, middleware_class: Type[BaseMiddleware]) -> None:
        """Register a middleware class."""
        cls._middleware_classes[name] = middleware_class
    
    @classmethod
    def create_middleware(cls, name: str, config: MiddlewareConfig = None) -> BaseMiddleware:
        """Create a middleware instance by name."""
        if name not in cls._middleware_classes:
            raise ValueError(f"Unknown middleware: {name}")
        
        middleware_class = cls._middleware_classes[name]
        return middleware_class(config)
    
    @classmethod
    def get_available_middlewares(cls) -> Dict[str, Type[BaseMiddleware]]:
        """Get all available middleware classes."""
        return cls._middleware_classes.copy()
    
    @classmethod
    def is_registered(cls, name: str) -> bool:
        """Check if a middleware is registered."""
        return name in cls._middleware_classes


def middleware_decorator(name: str):
    """
    Decorator to register a middleware class.
    
    Args:
        name: Middleware name for registration
    """
    def decorator(cls: Type[BaseMiddleware]) -> Type[BaseMiddleware]:
        MiddlewareFactory.register_middleware(name, cls)
        return cls
    
    return decorator


class MiddlewareMetrics:
    """Metrics collection for middleware performance."""
    
    def __init__(self):
        self._metrics: Dict[str, Dict[str, Any]] = {}
    
    def record_setup_time(self, middleware_name: str, setup_time: float) -> None:
        """Record middleware setup time."""
        if middleware_name not in self._metrics:
            self._metrics[middleware_name] = {}
        self._metrics[middleware_name]['setup_time'] = setup_time
    
    def record_request_count(self, middleware_name: str) -> None:
        """Record request count for middleware."""
        if middleware_name not in self._metrics:
            self._metrics[middleware_name] = {}
        
        count = self._metrics[middleware_name].get('request_count', 0)
        self._metrics[middleware_name]['request_count'] = count + 1
    
    def get_metrics(self, middleware_name: str = None) -> Dict[str, Any]:
        """Get metrics for a specific middleware or all middlewares."""
        if middleware_name:
            return self._metrics.get(middleware_name, {})
        return self._metrics.copy()
    
    def reset_metrics(self, middleware_name: str = None) -> None:
        """Reset metrics for a specific middleware or all middlewares."""
        if middleware_name:
            if middleware_name in self._metrics:
                self._metrics[middleware_name] = {}
        else:
            self._metrics.clear()


@singleton()
class MiddlewareMetricsCollector:
    """Global middleware metrics collector."""
    
    def __init__(self):
        self.metrics = MiddlewareMetrics()
    
    def get_metrics(self) -> MiddlewareMetrics:
        """Get the metrics instance."""
        return self.metrics
