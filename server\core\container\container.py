"""
Dependency injection container implementation.

This module provides the core DI container functionality for managing
service lifecycles and dependency resolution.
"""

import inspect
from functools import lru_cache
from typing import Any, Callable, Dict, Optional, Type, TypeVar, get_type_hints
from contextlib import asynccontextmanager
from .providers import ServiceProvider, LifecycleType

T = TypeVar('T')


class DIContainer:
    """Dependency injection container."""
    
    def __init__(self):
        self._services: Dict[str, ServiceProvider] = {}
        self._singletons: Dict[str, Any] = {}
        self._scoped_instances: Dict[str, Dict[str, Any]] = {}
        self._current_scope: Optional[str] = None
    
    def register_singleton(self, interface: Type[T], implementation: Type[T] = None) -> 'DIContainer':
        """
        Register a service as singleton.
        
        Args:
            interface: Service interface/abstract class
            implementation: Concrete implementation (defaults to interface)
            
        Returns:
            Self for method chaining
        """
        impl = implementation or interface
        key = self._get_service_key(interface)
        
        self._services[key] = ServiceProvider(
            interface=interface,
            implementation=impl,
            lifecycle=LifecycleType.SINGLETON
        )
        return self
    
    def register_scoped(self, interface: Type[T], implementation: Type[T] = None) -> 'DIContainer':
        """
        Register a service as scoped (per request).
        
        Args:
            interface: Service interface/abstract class
            implementation: Concrete implementation (defaults to interface)
            
        Returns:
            Self for method chaining
        """
        impl = implementation or interface
        key = self._get_service_key(interface)
        
        self._services[key] = ServiceProvider(
            interface=interface,
            implementation=impl,
            lifecycle=LifecycleType.SCOPED
        )
        return self
    
    def register_transient(self, interface: Type[T], implementation: Type[T] = None) -> 'DIContainer':
        """
        Register a service as transient (new instance each time).
        
        Args:
            interface: Service interface/abstract class
            implementation: Concrete implementation (defaults to interface)
            
        Returns:
            Self for method chaining
        """
        impl = implementation or interface
        key = self._get_service_key(interface)
        
        self._services[key] = ServiceProvider(
            interface=interface,
            implementation=impl,
            lifecycle=LifecycleType.TRANSIENT
        )
        return self
    
    def register_factory(self, interface: Type[T], factory: Callable[[], T]) -> 'DIContainer':
        """
        Register a service with a factory function.
        
        Args:
            interface: Service interface/abstract class
            factory: Factory function that creates the service
            
        Returns:
            Self for method chaining
        """
        key = self._get_service_key(interface)
        
        self._services[key] = ServiceProvider(
            interface=interface,
            implementation=None,
            lifecycle=LifecycleType.SINGLETON,
            factory=factory
        )
        return self
    
    def register_instance(self, interface: Type[T], instance: T) -> 'DIContainer':
        """
        Register a service instance.
        
        Args:
            interface: Service interface/abstract class
            instance: Service instance
            
        Returns:
            Self for method chaining
        """
        key = self._get_service_key(interface)
        self._singletons[key] = instance
        return self
    
    def resolve(self, interface: Type[T]) -> T:
        """
        Resolve a service instance.
        
        Args:
            interface: Service interface/abstract class to resolve
            
        Returns:
            Service instance
            
        Raises:
            ValueError: If service is not registered
        """
        key = self._get_service_key(interface)
        
        if key not in self._services:
            raise ValueError(f"Service {interface.__name__} is not registered")
        
        provider = self._services[key]
        
        # Handle different lifecycles
        if provider.lifecycle == LifecycleType.SINGLETON:
            return self._get_singleton(key, provider)
        elif provider.lifecycle == LifecycleType.SCOPED:
            return self._get_scoped(key, provider)
        else:  # TRANSIENT
            return self._create_instance(provider)
    
    def _get_singleton(self, key: str, provider: ServiceProvider) -> Any:
        """Get or create singleton instance."""
        if key not in self._singletons:
            self._singletons[key] = self._create_instance(provider)
        return self._singletons[key]
    
    def _get_scoped(self, key: str, provider: ServiceProvider) -> Any:
        """Get or create scoped instance."""
        if self._current_scope is None:
            raise ValueError("No active scope for scoped service")
        
        if self._current_scope not in self._scoped_instances:
            self._scoped_instances[self._current_scope] = {}
        
        scope_instances = self._scoped_instances[self._current_scope]
        
        if key not in scope_instances:
            scope_instances[key] = self._create_instance(provider)
        
        return scope_instances[key]
    
    def _create_instance(self, provider: ServiceProvider) -> Any:
        """Create service instance with dependency injection."""
        if provider.factory:
            return provider.factory()
        
        implementation = provider.implementation
        
        # Get constructor parameters
        signature = inspect.signature(implementation.__init__)
        parameters = signature.parameters
        
        # Skip 'self' parameter
        param_names = [name for name in parameters.keys() if name != 'self']
        
        if not param_names:
            # No dependencies
            return implementation()
        
        # Resolve dependencies
        type_hints = get_type_hints(implementation.__init__)
        kwargs = {}
        
        for param_name in param_names:
            if param_name in type_hints:
                param_type = type_hints[param_name]
                kwargs[param_name] = self.resolve(param_type)
        
        return implementation(**kwargs)
    
    @asynccontextmanager
    async def scope(self, scope_id: str = None):
        """
        Create a new dependency scope.
        
        Args:
            scope_id: Optional scope identifier
        """
        if scope_id is None:
            import uuid
            scope_id = str(uuid.uuid4())
        
        old_scope = self._current_scope
        self._current_scope = scope_id
        
        try:
            yield
        finally:
            # Cleanup scoped instances
            if scope_id in self._scoped_instances:
                del self._scoped_instances[scope_id]
            
            self._current_scope = old_scope
    
    def _get_service_key(self, interface: Type) -> str:
        """Get service key from interface type."""
        return f"{interface.__module__}.{interface.__name__}"
    
    def is_registered(self, interface: Type) -> bool:
        """Check if service is registered."""
        key = self._get_service_key(interface)
        return key in self._services
    
    def clear(self):
        """Clear all registrations and instances."""
        self._services.clear()
        self._singletons.clear()
        self._scoped_instances.clear()
        self._current_scope = None


# Global container instance
_container: Optional[DIContainer] = None


@lru_cache()
def get_container() -> DIContainer:
    """Get the global DI container instance."""
    global _container
    if _container is None:
        _container = DIContainer()
    return _container


def reset_container():
    """Reset the global container (mainly for testing)."""
    global _container
    if _container:
        _container.clear()
    _container = None
