"""
Cache management module for the FastAPI application.

This module provides:
- Redis connection management
- Cache abstraction layer
- Multi-level caching strategies
- Cache decorators for easy usage
- Cache invalidation strategies
"""

from .manager import CacheManager, get_cache_manager
from .strategies import CacheStrategy, TTLStrategy, LRUStrategy
from .decorators import cached, cache_invalidate, cache_key

try:
    from .redis_manager import RedisManager, get_redis_manager, AIOREDIS_AVAILABLE
    REDIS_AVAILABLE = AIOREDIS_AVAILABLE
    __all__ = [
        "CacheManager",
        "get_cache_manager",
        "CacheStrategy",
        "TTLStrategy",
        "LRUStrategy",
        "cached",
        "cache_invalidate",
        "cache_key",
        "RedisManager",
        "get_redis_manager",
        "REDIS_AVAILABLE"
    ]
except (ImportError, TypeError) as e:
    REDIS_AVAILABLE = False
    RedisManager = None
    get_redis_manager = None
    __all__ = [
        "CacheManager",
        "get_cache_manager",
        "CacheStrategy",
        "TTLStrategy",
        "LRUStrategy",
        "cached",
        "cache_invalidate",
        "cache_key",
        "REDIS_AVAILABLE"
    ]