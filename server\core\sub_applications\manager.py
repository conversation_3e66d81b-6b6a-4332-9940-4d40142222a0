"""
Sub-application manager for unified sub-application management.

This module provides a high-level interface for sub-application management,
including registration, configuration, and lifecycle management.
"""

from typing import Dict, List, Optional, Any, Type
from fastapi import FastAPI
from core.container import singleton
from core.config import get_app_config
from core.log import logger
from .base import BaseSubApplication, SubApplicationConfig
from .registry import SubApplicationRegistry


@singleton()
class SubApplicationManager:
    """
    High-level sub-application manager.
    
    Provides a unified interface for sub-application management,
    including automatic registration and configuration.
    """
    
    def __init__(self, registry: SubApplicationRegistry = None):
        self.registry = registry or SubApplicationRegistry()
        self.app_config = get_app_config()
        self._mount_completed = False
    
    def register_sub_application(self, sub_application: BaseSubApplication) -> None:
        """
        Register a sub-application.
        
        Args:
            sub_application: Sub-application instance to register
        """
        self.registry.register(sub_application)
    
    def register_sub_application_class(
        self,
        sub_app_class: Type[BaseSubApplication],
        config: SubApplicationConfig = None
    ) -> None:
        """
        Register a sub-application class with configuration.
        
        Args:
            sub_app_class: Sub-application class to instantiate and register
            config: Configuration for the sub-application
        """
        sub_application = sub_app_class(config)
        self.register_sub_application(sub_application)
    
    def unregister_sub_application(self, name: str) -> bool:
        """Unregister a sub-application by name."""
        return self.registry.unregister(name)
    
    def enable_sub_application(self, name: str) -> bool:
        """Enable a sub-application by name."""
        return self.registry.enable_sub_application(name)
    
    def disable_sub_application(self, name: str) -> bool:
        """Disable a sub-application by name."""
        return self.registry.disable_sub_application(name)
    
    def get_sub_application(self, name: str) -> Optional[BaseSubApplication]:
        """Get a sub-application by name."""
        return self.registry.get_sub_application(name)
    
    def list_sub_applications(self) -> Dict[str, Any]:
        """List all registered sub-applications with their information."""
        return self.registry.get_sub_application_info()
    
    def get_enabled_sub_applications(self) -> List[str]:
        """Get names of all enabled sub-applications."""
        return list(self.registry.get_enabled_sub_applications().keys())
    
    def get_mount_order(self) -> List[str]:
        """Get the sub-application mount order."""
        return self.registry.get_mount_order()
    
    def get_mount_paths(self) -> Dict[str, str]:
        """Get mapping of sub-application names to mount paths."""
        return self.registry.get_mount_paths()
    
    async def mount_sub_applications(self, app: FastAPI) -> None:
        """
        Mount all enabled sub-applications on the FastAPI application.
        
        Args:
            app: FastAPI application instance
        """
        if self._mount_completed:
            logger.warning("Sub-applications have already been mounted")
            return
        
        logger.info("Starting sub-application mounting...")
        
        # Validate mount paths before mounting
        conflicts = self.registry.validate_mount_paths()
        if conflicts:
            error_msg = "Mount path conflicts detected:\n" + "\n".join(conflicts)
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        try:
            await self.registry.mount_all(app)
            self._mount_completed = True
            logger.info("Sub-application mounting completed successfully")
        except Exception as e:
            logger.error(f"Sub-application mounting failed: {e}")
            raise
    
    def configure_sub_application(self, name: str, **config_updates) -> bool:
        """
        Update configuration for a specific sub-application.
        
        Args:
            name: Sub-application name
            **config_updates: Configuration updates
            
        Returns:
            True if configuration was updated, False if sub-application not found
        """
        sub_application = self.registry.get_sub_application(name)
        if not sub_application:
            return False
        
        sub_application.update_config(**config_updates)
        logger.info(f"Updated configuration for sub-application '{name}': {config_updates}")
        return True
    
    def load_default_sub_applications(self) -> None:
        """Load default sub-applications based on application configuration."""
        from .applications import (
            StaticFilesApplication,
            HealthCheckApplication,
            AdminPanelApplication,
            APIDocumentationApplication,
            MetricsApplication
        )
        
        # Register default sub-applications
        default_sub_applications = [
            (StaticFilesApplication, None),
            (HealthCheckApplication, None),
            (AdminPanelApplication, None),
            (APIDocumentationApplication, None),
            (MetricsApplication, None),
        ]
        
        for sub_app_class, config in default_sub_applications:
            try:
                self.register_sub_application_class(sub_app_class, config)
                logger.info(f"Registered default sub-application: {sub_app_class.__name__}")
            except Exception as e:
                logger.error(f"Failed to register default sub-application {sub_app_class.__name__}: {e}")
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get health status of all sub-applications."""
        status = {
            "mount_completed": self._mount_completed,
            "total_sub_applications": len(self.registry.get_all_sub_applications()),
            "enabled_sub_applications": len(self.registry.get_enabled_sub_applications()),
            "mount_order": self.registry.get_mount_order(),
            "mount_paths": self.registry.get_mount_paths(),
            "metrics": self.registry.get_metrics()
        }
        
        return status
    
    def find_by_mount_path(self, mount_path: str) -> Optional[BaseSubApplication]:
        """Find a sub-application by its mount path."""
        return self.registry.find_by_mount_path(mount_path)
    
    def export_configuration(self) -> Dict[str, Any]:
        """Export current sub-application configuration."""
        config = {}
        
        for name, sub_app in self.registry.get_all_sub_applications().items():
            config[name] = {
                "enabled": self.registry.is_enabled(name),
                "config": sub_app.get_config_dict(),
                "health_status": sub_app.get_health_status()
            }
        
        return config
    
    def import_configuration(self, config: Dict[str, Any]) -> None:
        """
        Import sub-application configuration.
        
        Args:
            config: Configuration dictionary to import
        """
        for name, sub_app_config in config.items():
            sub_application = self.registry.get_sub_application(name)
            if sub_application:
                # Update configuration
                if "config" in sub_app_config:
                    sub_application.update_config(**sub_app_config["config"])
                
                # Update enabled state
                if "enabled" in sub_app_config:
                    if sub_app_config["enabled"]:
                        self.registry.enable_sub_application(name)
                    else:
                        self.registry.disable_sub_application(name)
                
                logger.info(f"Imported configuration for sub-application: {name}")
            else:
                logger.warning(f"Sub-application '{name}' not found during configuration import")
    
    def get_sub_application_metrics(self) -> Dict[str, Any]:
        """Get detailed metrics for all sub-applications."""
        metrics = {}
        
        for name, sub_app in self.registry.get_all_sub_applications().items():
            metrics[name] = {
                "health_status": sub_app.get_health_status(),
                "mount_path": sub_app.get_mount_path(),
                "priority": sub_app.get_priority(),
                "enabled": self.registry.is_enabled(name),
                "mounted": sub_app.is_mounted()
            }
        
        # Add registry metrics
        metrics["_registry"] = self.registry.get_metrics()
        
        return metrics
    
    def validate_configuration(self) -> List[str]:
        """
        Validate all sub-application configurations.
        
        Returns:
            List of validation error messages
        """
        errors = []
        
        # Check for mount path conflicts
        conflicts = self.registry.validate_mount_paths()
        errors.extend(conflicts)
        
        # Validate individual sub-application configurations
        for name, sub_app in self.registry.get_all_sub_applications().items():
            if not sub_app.validate_config():
                errors.append(f"Invalid configuration for sub-application '{name}'")
        
        return errors


# Global sub-application manager instance
_sub_application_manager: Optional[SubApplicationManager] = None


def get_sub_application_manager() -> SubApplicationManager:
    """Get the global sub-application manager instance."""
    global _sub_application_manager
    if _sub_application_manager is None:
        _sub_application_manager = SubApplicationManager()
    return _sub_application_manager


def setup_sub_applications(app: FastAPI) -> None:
    """
    Convenience function to setup sub-applications on FastAPI app.
    
    Args:
        app: FastAPI application instance
    """
    import asyncio
    
    manager = get_sub_application_manager()
    
    # Load default sub-applications if none are registered
    if not manager.registry.get_all_sub_applications():
        manager.load_default_sub_applications()
    
    # Mount sub-applications
    asyncio.create_task(manager.mount_sub_applications(app))
