"""
Redis manager for cache operations.

This module provides an improved Redis manager with better connection
management, error handling, and caching strategies.
"""

import json
import pickle
from typing import Any, Optional, Union, Dict, List
from datetime import timedelta

# Handle redis import with compatibility check
try:
    import redis.asyncio as redis
    from redis.exceptions import RedisError, AuthenticationError, TimeoutError as RedisTimeoutError
    REDIS_AVAILABLE = True
except (ImportError, TypeError) as e:
    # Handle import errors
    REDIS_AVAILABLE = False
    redis = None
    RedisError = Exception
    AuthenticationError = Exception
    RedisTimeoutError = Exception

from core.config import get_redis_config
from core.container import singleton
from core.log import logger


@singleton()
class RedisManager:
    """
    Enhanced Redis manager with improved functionality.
    
    Features:
    - Connection pooling
    - Automatic reconnection
    - Serialization support (JSO<PERSON>, Pickle)
    - Batch operations
    - Pipeline support
    """
    
    def __init__(self, config=None):
        self.config = config or get_redis_config()
        self._redis: Optional[Any] = None
        self._connection_pool: Optional[Any] = None

    async def initialize(self) -> Any:
        """Initialize Redis connection with connection pool."""
        if not REDIS_AVAILABLE:
            logger.warning("redis不可用，无法初始化Redis连接")
            raise ImportError("redis package is not available")

        if self._redis is not None:
            return self._redis

        try:
            logger.info("开始连接Redis...")

            # Create connection pool
            self._connection_pool = redis.ConnectionPool.from_url(
                self.config.redis_url,
                max_connections=self.config.redis_max_connections,
                socket_timeout=self.config.redis_socket_timeout,
                socket_connect_timeout=self.config.redis_socket_connect_timeout,
                encoding='utf-8',
                decode_responses=True
            )

            # Create Redis client
            self._redis = redis.Redis(connection_pool=self._connection_pool)

            # Test connection
            await self._redis.ping()
            logger.info("Redis连接成功")

            return self._redis

        except AuthenticationError as e:
            logger.error(f"Redis用户名或密码错误，详细错误信息：{e}")
            raise
        except RedisTimeoutError as e:
            logger.error(f"Redis连接超时，详细错误信息：{e}")
            raise
        except RedisError as e:
            logger.error(f"Redis连接错误，详细错误信息：{e}")
            raise
    
    async def get_redis(self) -> Any:
        """Get Redis client instance."""
        if self._redis is None:
            await self.initialize()
        return self._redis
    
    async def close(self):
        """Close Redis connection."""
        if self._redis:
            await self._redis.aclose()
            self._redis = None

        if self._connection_pool:
            await self._connection_pool.aclose()
            self._connection_pool = None

        logger.info("Redis连接已关闭")
    
    # Basic operations
    async def set(
        self, 
        key: str, 
        value: Any, 
        expire: Optional[Union[int, timedelta]] = None,
        serialize: bool = True
    ) -> bool:
        """
        Set key-value pair in Redis.
        
        Args:
            key: Redis key
            value: Value to store
            expire: Expiration time in seconds or timedelta
            serialize: Whether to serialize the value
            
        Returns:
            True if successful
        """
        redis = await self.get_redis()
        
        if serialize:
            value = self._serialize(value)
        
        if isinstance(expire, timedelta):
            expire = int(expire.total_seconds())
        
        return await redis.set(key, value, ex=expire)
    
    async def get(self, key: str, deserialize: bool = True) -> Any:
        """
        Get value from Redis.
        
        Args:
            key: Redis key
            deserialize: Whether to deserialize the value
            
        Returns:
            Value or None if not found
        """
        redis = await self.get_redis()
        value = await redis.get(key)
        
        if value is None:
            return None
        
        if deserialize:
            return self._deserialize(value)
        
        return value
    
    async def delete(self, *keys: str) -> int:
        """Delete keys from Redis."""
        redis = await self.get_redis()
        return await redis.delete(*keys)
    
    async def exists(self, key: str) -> bool:
        """Check if key exists in Redis."""
        redis = await self.get_redis()
        return bool(await redis.exists(key))
    
    async def expire(self, key: str, seconds: Union[int, timedelta]) -> bool:
        """Set expiration time for key."""
        redis = await self.get_redis()
        
        if isinstance(seconds, timedelta):
            seconds = int(seconds.total_seconds())
        
        return await redis.expire(key, seconds)
    
    async def ttl(self, key: str) -> int:
        """Get time to live for key."""
        redis = await self.get_redis()
        return await redis.ttl(key)
    
    # Hash operations
    async def hset(self, name: str, mapping: Dict[str, Any], serialize: bool = True) -> int:
        """Set hash fields."""
        redis = await self.get_redis()
        
        if serialize:
            mapping = {k: self._serialize(v) for k, v in mapping.items()}
        
        return await redis.hset(name, mapping=mapping)
    
    async def hget(self, name: str, key: str, deserialize: bool = True) -> Any:
        """Get hash field value."""
        redis = await self.get_redis()
        value = await redis.hget(name, key)
        
        if value is None:
            return None
        
        if deserialize:
            return self._deserialize(value)
        
        return value
    
    async def hgetall(self, name: str, deserialize: bool = True) -> Dict[str, Any]:
        """Get all hash fields."""
        redis = await self.get_redis()
        data = await redis.hgetall(name)
        
        if deserialize:
            return {k: self._deserialize(v) for k, v in data.items()}
        
        return data
    
    async def hdel(self, name: str, *keys: str) -> int:
        """Delete hash fields."""
        redis = await self.get_redis()
        return await redis.hdel(name, *keys)
    
    # List operations
    async def lpush(self, name: str, *values: Any, serialize: bool = True) -> int:
        """Push values to the left of list."""
        redis = await self.get_redis()
        
        if serialize:
            values = [self._serialize(v) for v in values]
        
        return await redis.lpush(name, *values)
    
    async def rpush(self, name: str, *values: Any, serialize: bool = True) -> int:
        """Push values to the right of list."""
        redis = await self.get_redis()
        
        if serialize:
            values = [self._serialize(v) for v in values]
        
        return await redis.rpush(name, *values)
    
    async def lpop(self, name: str, deserialize: bool = True) -> Any:
        """Pop value from the left of list."""
        redis = await self.get_redis()
        value = await redis.lpop(name)
        
        if value is None:
            return None
        
        if deserialize:
            return self._deserialize(value)
        
        return value
    
    async def rpop(self, name: str, deserialize: bool = True) -> Any:
        """Pop value from the right of list."""
        redis = await self.get_redis()
        value = await redis.rpop(name)
        
        if value is None:
            return None
        
        if deserialize:
            return self._deserialize(value)
        
        return value
    
    async def lrange(self, name: str, start: int, end: int, deserialize: bool = True) -> List[Any]:
        """Get list range."""
        redis = await self.get_redis()
        values = await redis.lrange(name, start, end)
        
        if deserialize:
            return [self._deserialize(v) for v in values]
        
        return values
    
    # Utility methods
    def _serialize(self, value: Any) -> str:
        """Serialize value for Redis storage."""
        try:
            return json.dumps(value, ensure_ascii=False)
        except (TypeError, ValueError):
            # Fallback to pickle for complex objects
            return pickle.dumps(value).hex()
    
    def _deserialize(self, value: str) -> Any:
        """Deserialize value from Redis."""
        try:
            return json.loads(value)
        except (json.JSONDecodeError, TypeError):
            try:
                # Try pickle deserialization
                return pickle.loads(bytes.fromhex(value))
            except (ValueError, pickle.PickleError):
                # Return as string if all else fails
                return value
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform Redis health check."""
        try:
            redis = await self.get_redis()
            
            # Test basic operations
            test_key = "health_check_test"
            await redis.set(test_key, "test", ex=10)
            value = await redis.get(test_key)
            await redis.delete(test_key)
            
            return {
                "status": "healthy" if value == "test" else "unhealthy",
                "connection": True,
                "operations": value == "test"
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "connection": False,
                "operations": False,
                "error": str(e)
            }


# Global Redis manager instance
_redis_manager: Optional[RedisManager] = None


def get_redis_manager() -> RedisManager:
    """Get global Redis manager instance."""
    if not REDIS_AVAILABLE:
        raise ImportError("redis package is not available")

    global _redis_manager
    if _redis_manager is None:
        _redis_manager = RedisManager()
    return _redis_manager


async def reset_redis_manager():
    """Reset Redis manager (mainly for testing)."""
    global _redis_manager
    if _redis_manager:
        await _redis_manager.close()
    _redis_manager = None


# Export availability flag
__all__ = ["RedisManager", "get_redis_manager", "reset_redis_manager", "REDIS_AVAILABLE"]
