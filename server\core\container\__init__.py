"""
Dependency injection container for the FastAPI application.

This module provides a modern dependency injection system with:
- Service lifecycle management (singleton, scoped, transient)
- Interface-based dependency resolution
- Automatic dependency graph resolution
- Integration with FastAPI's dependency injection
"""

from .container import DIContainer, get_container
from .providers import ServiceProvider, LifecycleType
from .decorators import injectable, inject, singleton, scoped, transient

__all__ = [
    "DIContainer",
    "get_container",
    "ServiceProvider",
    "LifecycleType",
    "injectable",
    "inject",
    "singleton",
    "scoped",
    "transient"
]
