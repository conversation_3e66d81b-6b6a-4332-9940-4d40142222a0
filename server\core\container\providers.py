"""
Service providers for dependency injection.

This module defines service provider classes and lifecycle types
for the dependency injection container.
"""

from enum import Enum
from typing import Any, Callable, Optional, Type
from dataclasses import dataclass


class LifecycleType(Enum):
    """Service lifecycle types."""
    
    SINGLETON = "singleton"     # Single instance for entire application
    SCOPED = "scoped"          # Single instance per scope (e.g., per request)
    TRANSIENT = "transient"    # New instance every time


@dataclass
class ServiceProvider:
    """Service provider configuration."""
    
    interface: Type
    implementation: Optional[Type] = None
    lifecycle: LifecycleType = LifecycleType.TRANSIENT
    factory: Optional[Callable[[], Any]] = None
    
    def __post_init__(self):
        """Validate provider configuration."""
        if self.implementation is None and self.factory is None:
            raise ValueError("Either implementation or factory must be provided")
        
        if self.implementation is not None and self.factory is not None:
            raise ValueError("Cannot provide both implementation and factory")


class ServiceRegistry:
    """Registry for managing service providers."""
    
    def __init__(self):
        self._providers: dict[str, ServiceProvider] = {}
    
    def register(self, provider: ServiceProvider) -> None:
        """Register a service provider."""
        key = self._get_key(provider.interface)
        self._providers[key] = provider
    
    def get_provider(self, interface: Type) -> Optional[ServiceProvider]:
        """Get service provider for interface."""
        key = self._get_key(interface)
        return self._providers.get(key)
    
    def is_registered(self, interface: Type) -> bool:
        """Check if interface is registered."""
        key = self._get_key(interface)
        return key in self._providers
    
    def get_all_providers(self) -> dict[str, ServiceProvider]:
        """Get all registered providers."""
        return self._providers.copy()
    
    def clear(self) -> None:
        """Clear all providers."""
        self._providers.clear()
    
    def _get_key(self, interface: Type) -> str:
        """Get key for interface type."""
        return f"{interface.__module__}.{interface.__name__}"


class ServiceScope:
    """Service scope for managing scoped instances."""
    
    def __init__(self, scope_id: str):
        self.scope_id = scope_id
        self._instances: dict[str, Any] = {}
    
    def get_instance(self, key: str) -> Optional[Any]:
        """Get instance from scope."""
        return self._instances.get(key)
    
    def set_instance(self, key: str, instance: Any) -> None:
        """Set instance in scope."""
        self._instances[key] = instance
    
    def has_instance(self, key: str) -> bool:
        """Check if instance exists in scope."""
        return key in self._instances
    
    def clear(self) -> None:
        """Clear all instances in scope."""
        self._instances.clear()
    
    def get_instance_count(self) -> int:
        """Get number of instances in scope."""
        return len(self._instances)


class FactoryProvider:
    """Provider for factory-based services."""
    
    def __init__(self, factory: Callable[[], Any], lifecycle: LifecycleType = LifecycleType.TRANSIENT):
        self.factory = factory
        self.lifecycle = lifecycle
        self._singleton_instance: Optional[Any] = None
    
    def create_instance(self) -> Any:
        """Create service instance."""
        if self.lifecycle == LifecycleType.SINGLETON:
            if self._singleton_instance is None:
                self._singleton_instance = self.factory()
            return self._singleton_instance
        else:
            return self.factory()
    
    def reset_singleton(self) -> None:
        """Reset singleton instance."""
        self._singleton_instance = None


class InstanceProvider:
    """Provider for pre-created instances."""
    
    def __init__(self, instance: Any):
        self.instance = instance
        self.lifecycle = LifecycleType.SINGLETON
    
    def get_instance(self) -> Any:
        """Get the instance."""
        return self.instance


class TypeProvider:
    """Provider for type-based services."""
    
    def __init__(self, implementation: Type, lifecycle: LifecycleType = LifecycleType.TRANSIENT):
        self.implementation = implementation
        self.lifecycle = lifecycle
        self._singleton_instance: Optional[Any] = None
    
    def create_instance(self, resolver: Callable[[Type], Any]) -> Any:
        """Create service instance with dependency resolution."""
        if self.lifecycle == LifecycleType.SINGLETON:
            if self._singleton_instance is None:
                self._singleton_instance = self._instantiate(resolver)
            return self._singleton_instance
        else:
            return self._instantiate(resolver)
    
    def _instantiate(self, resolver: Callable[[Type], Any]) -> Any:
        """Instantiate the implementation with dependency injection."""
        import inspect
        from typing import get_type_hints
        
        # Get constructor parameters
        signature = inspect.signature(self.implementation.__init__)
        parameters = signature.parameters
        
        # Skip 'self' parameter
        param_names = [name for name in parameters.keys() if name != 'self']
        
        if not param_names:
            # No dependencies
            return self.implementation()
        
        # Resolve dependencies
        type_hints = get_type_hints(self.implementation.__init__)
        kwargs = {}
        
        for param_name in param_names:
            if param_name in type_hints:
                param_type = type_hints[param_name]
                kwargs[param_name] = resolver(param_type)
        
        return self.implementation(**kwargs)
    
    def reset_singleton(self) -> None:
        """Reset singleton instance."""
        self._singleton_instance = None
