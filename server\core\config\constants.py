"""
Application constants and enums.

This module contains all constant values used throughout the application,
organized by functional areas.
"""

from enum import Enum
from typing import Final


class CommonConstants:
    """Common application constants."""
    
    # Protocol constants
    WWW: Final[str] = "www."
    HTTP: Final[str] = "http://"
    HTTPS: Final[str] = "https://"
    LOOKUP_RMI: Final[str] = "rmi:"
    LOOKUP_LDAP: Final[str] = "ldap:"
    LOOKUP_LDAPS: Final[str] = "ldaps:"
    
    # Boolean constants
    YES: Final[str] = "Y"
    NO: Final[str] = "N"
    
    # Status constants
    DEPT_NORMAL: Final[str] = "0"
    DEPT_DISABLE: Final[str] = "1"
    
    # Validation constants
    UNIQUE: Final[bool] = True
    NOT_UNIQUE: Final[bool] = False


class HttpStatusConstants:
    """HTTP status code constants."""
    
    # Success codes
    SUCCESS: Final[int] = 200
    CREATED: Final[int] = 201
    ACCEPTED: Final[int] = 202
    NO_CONTENT: Final[int] = 204
    
    # Redirect codes
    MOVED_PERM: Final[int] = 301
    SEE_OTHER: Final[int] = 303
    NOT_MODIFIED: Final[int] = 304
    
    # Client error codes
    BAD_REQUEST: Final[int] = 400
    UNAUTHORIZED: Final[int] = 401
    FORBIDDEN: Final[int] = 403
    NOT_FOUND: Final[int] = 404
    BAD_METHOD: Final[int] = 405
    CONFLICT: Final[int] = 409
    UNSUPPORTED_TYPE: Final[int] = 415
    
    # Server error codes
    ERROR: Final[int] = 500
    NOT_IMPLEMENTED: Final[int] = 501
    
    # Custom codes
    WARN: Final[int] = 601


class JobConstants:
    """Scheduled job constants."""
    
    # Prohibited modules and characters for job execution
    JOB_ERROR_LIST: Final[list[str]] = [
        "app", "config", "exceptions", "import ", "middlewares",
        "module_admin", "open(", "os.", "server", "sub_applications",
        "subprocess.", "sys.", "utils", "while ", "__import__",
        '"', "'", ",", "?", ":", ";", "/", "|", "+", "-", "=",
        "~", "!", "#", "$", "%", "^", "&", "*", "<", ">",
        "(", ")", "[", "]", "{", "}", " "
    ]
    
    # Allowed modules for job execution
    JOB_WHITE_LIST: Final[list[str]] = ["module_task"]


class MenuConstants:
    """Menu system constants."""
    
    # Menu types
    TYPE_DIR: Final[str] = "M"      # Directory
    TYPE_MENU: Final[str] = "C"     # Menu
    TYPE_BUTTON: Final[str] = "F"   # Button
    
    # Frame settings
    YES_FRAME: Final[int] = 0       # External link
    NO_FRAME: Final[int] = 1        # Internal link
    
    # Component identifiers
    LAYOUT: Final[str] = "Layout"
    PARENT_VIEW: Final[str] = "ParentView"
    INNER_LINK: Final[str] = "InnerLink"


class GenConstants:
    """Code generation constants."""
    
    # Template types
    TPL_CRUD: Final[str] = "crud"
    TPL_TREE: Final[str] = "tree"
    TPL_SUB: Final[str] = "sub"
    
    # Tree options
    TREE_CODE: Final[str] = "treeCode"
    TREE_PARENT_CODE: Final[str] = "treeParentCode"
    TREE_NAME: Final[str] = "treeName"
    
    # Query types
    QUERY_LIKE: Final[str] = "LIKE"
    QUERY_EQ: Final[str] = "EQ"
    QUERY_NE: Final[str] = "NE"
    QUERY_GT: Final[str] = "GT"
    QUERY_GTE: Final[str] = "GTE"
    QUERY_LT: Final[str] = "LT"
    QUERY_LTE: Final[str] = "LTE"
    QUERY_BETWEEN: Final[str] = "BETWEEN"
    
    # Display types
    DISPLAY_INPUT: Final[str] = "input"
    DISPLAY_TEXTAREA: Final[str] = "textarea"
    DISPLAY_SELECT: Final[str] = "select"
    DISPLAY_RADIO: Final[str] = "radio"
    DISPLAY_CHECKBOX: Final[str] = "checkbox"
    DISPLAY_DATETIME: Final[str] = "datetime"
    DISPLAY_IMAGE_UPLOAD: Final[str] = "imageUpload"
    DISPLAY_FILE_UPLOAD: Final[str] = "fileUpload"
    DISPLAY_EDITOR: Final[str] = "editor"
    
    # Required field
    REQUIRE: Final[str] = "1"


class BusinessType(Enum):
    """Business operation types for logging."""
    
    OTHER = (0, "其他")
    INSERT = (1, "新增")
    UPDATE = (2, "修改")
    DELETE = (3, "删除")
    GRANT = (4, "授权")
    EXPORT = (5, "导出")
    IMPORT = (6, "导入")
    FORCE = (7, "强退")
    GENCODE = (8, "生成代码")
    CLEAN = (9, "清空数据")
    
    def __init__(self, code: int, description: str):
        self.code = code
        self.description = description


class RedisKeyConfig(Enum):
    """Redis key configuration."""
    
    ACCESS_TOKEN = ("access_token", "登录令牌信息")
    SYS_DICT = ("sys_dict", "数据字典")
    SYS_CONFIG = ("sys_config", "配置信息")
    CAPTCHA_CODES = ("captcha_codes", "图片验证码")
    ACCOUNT_LOCK = ("account_lock", "用户锁定")
    PASSWORD_ERROR_COUNT = ("password_error_count", "密码错误次数")
    SMS_CODE = ("sms_code", "短信验证码")
    
    def __init__(self, key: str, remark: str):
        self.key = key
        self.remark = remark


class ErrorCode(Enum):
    """Application error codes."""
    
    # Authentication errors (1000-1999)
    AUTH_TOKEN_INVALID = (1001, "令牌无效")
    AUTH_TOKEN_EXPIRED = (1002, "令牌已过期")
    AUTH_LOGIN_FAILED = (1003, "登录失败")
    AUTH_PERMISSION_DENIED = (1004, "权限不足")
    
    # User errors (2000-2999)
    USER_NOT_FOUND = (2001, "用户不存在")
    USER_ALREADY_EXISTS = (2002, "用户已存在")
    USER_DISABLED = (2003, "用户已被禁用")
    USER_PASSWORD_ERROR = (2004, "密码错误")
    
    # Business errors (3000-3999)
    BUSINESS_ERROR = (3001, "业务处理失败")
    DATA_NOT_FOUND = (3002, "数据不存在")
    DATA_ALREADY_EXISTS = (3003, "数据已存在")
    OPERATION_FAILED = (3004, "操作失败")
    
    # System errors (5000-5999)
    DATABASE_ERROR = (5001, "数据库错误")
    REDIS_ERROR = (5002, "缓存服务错误")
    NETWORK_ERROR = (5003, "网络错误")
    FILE_ERROR = (5004, "文件操作错误")
    
    def __init__(self, code: int, message: str):
        self.code = code
        self.message = message


class CachePathConfig:
    """Cache directory configuration."""

    import os
    PATH: Final[str] = os.path.join(os.path.abspath(os.getcwd()), 'caches')
    PATHSTR: Final[str] = 'caches'
