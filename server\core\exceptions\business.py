"""
Business exception classes.

This module defines business-specific exceptions that represent
various business logic errors and conditions.
"""

from typing import Any, Dict, Optional
from .base import BaseApplicationException
from core.config.constants import ErrorCode


class BusinessException(BaseApplicationException):
    """
    Base class for business logic exceptions.
    
    Used for general business rule violations.
    """
    
    def __init__(
        self,
        message: str = "Business logic error",
        error_code: ErrorCode = None,
        data: Any = None,
        status_code: int = 400
    ):
        super().__init__(
            message=message,
            error_code=error_code or ErrorCode.BUSINESS_ERROR,
            data=data,
            status_code=status_code
        )


class AuthenticationException(BaseApplicationException):
    """
    Exception for authentication failures.
    
    Used when user authentication fails.
    """
    
    def __init__(
        self,
        message: str = "Authentication failed",
        username: str = None,
        data: Any = None
    ):
        super().__init__(
            message=message,
            error_code=ErrorCode.AUTH_LOGIN_FAILED,
            data=data,
            status_code=401
        )
        self.username = username
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert authentication exception to dictionary."""
        result = super().to_dict()
        
        if self.username:
            result["username"] = self.username
        
        return result


class AuthorizationException(BaseApplicationException):
    """
    Exception for authorization failures.
    
    Used when user lacks required permissions.
    """
    
    def __init__(
        self,
        message: str = "Access denied",
        required_permission: str = None,
        user_id: str = None,
        data: Any = None
    ):
        super().__init__(
            message=message,
            error_code=ErrorCode.AUTH_PERMISSION_DENIED,
            data=data,
            status_code=403
        )
        self.required_permission = required_permission
        self.user_id = user_id
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert authorization exception to dictionary."""
        result = super().to_dict()
        
        if self.required_permission:
            result["required_permission"] = self.required_permission
        
        if self.user_id:
            result["user_id"] = self.user_id
        
        return result


class ResourceNotFoundException(BaseApplicationException):
    """
    Exception for resource not found errors.
    
    Used when a requested resource cannot be found.
    """
    
    def __init__(
        self,
        message: str = "Resource not found",
        resource_type: str = None,
        resource_id: str = None,
        data: Any = None
    ):
        super().__init__(
            message=message,
            error_code=ErrorCode.DATA_NOT_FOUND,
            data=data,
            status_code=404
        )
        self.resource_type = resource_type
        self.resource_id = resource_id
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert resource not found exception to dictionary."""
        result = super().to_dict()
        
        if self.resource_type:
            result["resource_type"] = self.resource_type
        
        if self.resource_id:
            result["resource_id"] = self.resource_id
        
        return result


class ResourceConflictException(BaseApplicationException):
    """
    Exception for resource conflict errors.
    
    Used when a resource already exists or conflicts with existing data.
    """
    
    def __init__(
        self,
        message: str = "Resource conflict",
        resource_type: str = None,
        resource_id: str = None,
        conflict_field: str = None,
        data: Any = None
    ):
        super().__init__(
            message=message,
            error_code=ErrorCode.DATA_ALREADY_EXISTS,
            data=data,
            status_code=409
        )
        self.resource_type = resource_type
        self.resource_id = resource_id
        self.conflict_field = conflict_field
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert resource conflict exception to dictionary."""
        result = super().to_dict()
        
        if self.resource_type:
            result["resource_type"] = self.resource_type
        
        if self.resource_id:
            result["resource_id"] = self.resource_id
        
        if self.conflict_field:
            result["conflict_field"] = self.conflict_field
        
        return result


class BusinessLogicException(BaseApplicationException):
    """
    Exception for business logic violations.
    
    Used when business rules are violated.
    """
    
    def __init__(
        self,
        message: str = "Business rule violation",
        rule: str = None,
        context: Dict[str, Any] = None,
        data: Any = None
    ):
        super().__init__(
            message=message,
            error_code=ErrorCode.OPERATION_FAILED,
            data=data,
            status_code=422
        )
        self.rule = rule
        self.context = context or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert business logic exception to dictionary."""
        result = super().to_dict()
        
        if self.rule:
            result["rule"] = self.rule
        
        if self.context:
            result["context"] = self.context
        
        return result


class UserException(BaseApplicationException):
    """
    Exception for user-related errors.
    
    Used for user management operations.
    """
    
    def __init__(
        self,
        message: str = "User error",
        user_id: str = None,
        username: str = None,
        error_code: ErrorCode = None,
        data: Any = None,
        status_code: int = 400
    ):
        super().__init__(
            message=message,
            error_code=error_code or ErrorCode.USER_NOT_FOUND,
            data=data,
            status_code=status_code
        )
        self.user_id = user_id
        self.username = username
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert user exception to dictionary."""
        result = super().to_dict()
        
        if self.user_id:
            result["user_id"] = self.user_id
        
        if self.username:
            result["username"] = self.username
        
        return result


class TokenException(BaseApplicationException):
    """
    Exception for token-related errors.
    
    Used for JWT token validation and management.
    """
    
    def __init__(
        self,
        message: str = "Token error",
        token_type: str = None,
        error_code: ErrorCode = None,
        data: Any = None
    ):
        super().__init__(
            message=message,
            error_code=error_code or ErrorCode.AUTH_TOKEN_INVALID,
            data=data,
            status_code=401
        )
        self.token_type = token_type
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert token exception to dictionary."""
        result = super().to_dict()
        
        if self.token_type:
            result["token_type"] = self.token_type
        
        return result


# Legacy exception classes for backward compatibility
class LoginException(AuthenticationException):
    """Legacy login exception for backward compatibility."""
    pass


class AuthException(TokenException):
    """Legacy auth exception for backward compatibility."""
    pass


class PermissionException(AuthorizationException):
    """Legacy permission exception for backward compatibility."""
    pass


class ServiceException(BusinessException):
    """Legacy service exception for backward compatibility."""
    pass


class ServiceWarning(BusinessException):
    """Legacy service warning for backward compatibility."""
    
    def __init__(
        self,
        message: str = "Service warning",
        data: Any = None
    ):
        super().__init__(
            message=message,
            data=data,
            status_code=200  # Warning, not an error
        )


class ModelValidatorException(BusinessException):
    """Legacy model validator exception for backward compatibility."""
    pass
