"""
Exception handling module for the FastAPI application.

This module provides:
- Hierarchical exception system
- Unified error codes
- Exception handlers
- Structured error responses
"""

from .base import (
    BaseApplicationException,
    ValidationException,
    ConfigurationException
)
from .business import (
    BusinessException,
    AuthenticationException,
    AuthorizationException,
    ResourceNotFoundException,
    ResourceConflictException,
    BusinessLogicException
)
from .handlers import (
    ExceptionHandlerManager,
    register_exception_handlers,
    get_exception_handler_manager
)
from .codes import ErrorCode

__all__ = [
    # Base exceptions
    "BaseApplicationException",
    "ValidationException",
    "ConfigurationException",

    # Business exceptions
    "BusinessException",
    "AuthenticationException",
    "AuthorizationException",
    "ResourceNotFoundException",
    "ResourceConflictException",
    "BusinessLogicException",

    # Exception handling
    "ExceptionHandlerManager",
    "register_exception_handlers",
    "get_exception_handler_manager",

    # Error codes
    "ErrorCode"
]