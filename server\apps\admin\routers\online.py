from fastapi import APIRouter, Depends, Request
from sqlalchemy.ext.asyncio import AsyncSession
from core.config.constants import BusinessType
from core.database import get_database_session as get_db
from apps.admin.annotation.log_annotation import Log
from apps.admin.aspect.interface_auth import CheckUserInterfaceAuth
from apps.admin.schemas.online import DeleteOnlineSchema, OnlineQuerySchema
from apps.admin.services.login import LoginService
from apps.admin.services.online import OnlineService
from core.log import logger
from utils.page import PageResponseModel
from utils.response import ResponseUtil


router = APIRouter(prefix='/monitor/online', dependencies=[Depends(LoginService.get_current_user)])


@router.get(
    '/list', response_model=PageResponseModel, dependencies=[Depends(CheckUserInterfaceAuth('monitor:online:list'))]
)
async def get_monitor_online_list(
    request: Request, online_page_query: OnlineQuerySchema = Depends(OnlineQuerySchema.as_query)
):
    # 获取全量数据
    online_query_result = await OnlineService.get_online_list_services(request, online_page_query)
    logger.info('获取成功')

    return ResponseUtil.success(
        model_content=PageResponseModel(rows=online_query_result, total=len(online_query_result))
    )


@router.delete('/{token_ids}', dependencies=[Depends(CheckUserInterfaceAuth('monitor:online:forceLogout'))])
@Log(title='在线用户', business_type=BusinessType.FORCE)
async def delete_monitor_online(request: Request, token_ids: str, query_db: AsyncSession = Depends(get_db)):
    delete_online = DeleteOnlineSchema(tokenIds=token_ids)
    delete_online_result = await OnlineService.delete_online_services(request, delete_online)
    logger.info(delete_online_result.message)

    return ResponseUtil.success(msg=delete_online_result.message)
