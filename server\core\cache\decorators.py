"""
Cache decorators for easy caching functionality.

This module provides decorators to add caching to functions and methods.
"""

import functools
import hashlib
import json
from typing import Any, Callable, Optional, Union
from datetime import timedelta
from .manager import get_cache_manager


def cache_key(*args, **kwargs) -> str:
    """Generate cache key from function arguments."""
    # Create a deterministic key from arguments
    key_data = {
        'args': args,
        'kwargs': sorted(kwargs.items())
    }
    key_str = json.dumps(key_data, sort_keys=True, default=str)
    return hashlib.md5(key_str.encode()).hexdigest()


def cached(
    expire: Optional[Union[int, timedelta]] = None,
    key_prefix: str = "",
    key_func: Optional[Callable] = None
):
    """
    Decorator to cache function results.
    
    Args:
        expire: Cache expiration time
        key_prefix: Prefix for cache keys
        key_func: Custom function to generate cache keys
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            cache_manager = get_cache_manager()
            
            # Generate cache key
            if key_func:
                cache_key_str = key_func(*args, **kwargs)
            else:
                cache_key_str = cache_key(*args, **kwargs)
            
            full_key = f"{key_prefix}{func.__name__}:{cache_key_str}"
            
            # Try to get from cache
            cached_result = await cache_manager.get(full_key)
            if cached_result is not None:
                return cached_result
            
            # Execute function and cache result
            result = await func(*args, **kwargs)
            await cache_manager.set(full_key, result, expire)
            
            return result
        
        return wrapper
    return decorator


def cache_invalidate(
    key_pattern: str = None,
    key_func: Optional[Callable] = None
):
    """
    Decorator to invalidate cache after function execution.
    
    Args:
        key_pattern: Pattern of keys to invalidate
        key_func: Custom function to generate keys to invalidate
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            result = await func(*args, **kwargs)
            
            cache_manager = get_cache_manager()
            
            # Generate keys to invalidate
            if key_func:
                keys_to_invalidate = key_func(*args, **kwargs)
            elif key_pattern:
                keys_to_invalidate = [key_pattern]
            else:
                # Default: invalidate based on function name
                keys_to_invalidate = [f"{func.__name__}:*"]
            
            # Invalidate cache keys
            for key in keys_to_invalidate:
                await cache_manager.delete(key)
            
            return result
        
        return wrapper
    return decorator
