"""
Base exception classes for the application.

This module defines the base exception hierarchy that all other
application exceptions inherit from.
"""

from typing import Any, Dict, Optional
from core.config.constants import ErrorCode


class BaseApplicationException(Exception):
    """
    Base exception class for all application exceptions.
    
    Provides common functionality for error handling including:
    - Error codes
    - Error messages
    - Additional data
    - HTTP status codes
    """
    
    def __init__(
        self,
        message: str = None,
        error_code: ErrorCode = None,
        data: Any = None,
        status_code: int = 500
    ):
        self.message = message or "An error occurred"
        self.error_code = error_code
        self.data = data
        self.status_code = status_code
        super().__init__(self.message)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for API responses."""
        result = {
            "message": self.message,
            "status_code": self.status_code
        }
        
        if self.error_code:
            result.update({
                "error_code": self.error_code.code,
                "error_message": self.error_code.message
            })
        
        if self.data is not None:
            result["data"] = self.data
        
        return result
    
    def __str__(self) -> str:
        """String representation of the exception."""
        if self.error_code:
            return f"{self.error_code.code}: {self.message}"
        return self.message
    
    def __repr__(self) -> str:
        """Detailed representation of the exception."""
        return (
            f"{self.__class__.__name__}("
            f"message='{self.message}', "
            f"error_code={self.error_code}, "
            f"status_code={self.status_code})"
        )


class ValidationException(BaseApplicationException):
    """
    Exception for validation errors.
    
    Used when input data fails validation checks.
    """
    
    def __init__(
        self,
        message: str = "Validation failed",
        field: str = None,
        value: Any = None,
        data: Any = None
    ):
        super().__init__(
            message=message,
            error_code=ErrorCode.BUSINESS_ERROR,
            data=data,
            status_code=400
        )
        self.field = field
        self.value = value
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert validation exception to dictionary."""
        result = super().to_dict()
        
        if self.field:
            result["field"] = self.field
        
        if self.value is not None:
            result["value"] = self.value
        
        return result


class ConfigurationException(BaseApplicationException):
    """
    Exception for configuration errors.
    
    Used when there are issues with application configuration.
    """
    
    def __init__(
        self,
        message: str = "Configuration error",
        config_key: str = None,
        data: Any = None
    ):
        super().__init__(
            message=message,
            error_code=ErrorCode.SYSTEM_ERROR,
            data=data,
            status_code=500
        )
        self.config_key = config_key
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration exception to dictionary."""
        result = super().to_dict()
        
        if self.config_key:
            result["config_key"] = self.config_key
        
        return result


class DatabaseException(BaseApplicationException):
    """
    Exception for database-related errors.
    
    Used when database operations fail.
    """
    
    def __init__(
        self,
        message: str = "Database error",
        operation: str = None,
        data: Any = None
    ):
        super().__init__(
            message=message,
            error_code=ErrorCode.DATABASE_ERROR,
            data=data,
            status_code=500
        )
        self.operation = operation
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert database exception to dictionary."""
        result = super().to_dict()
        
        if self.operation:
            result["operation"] = self.operation
        
        return result


class CacheException(BaseApplicationException):
    """
    Exception for cache-related errors.
    
    Used when cache operations fail.
    """
    
    def __init__(
        self,
        message: str = "Cache error",
        operation: str = None,
        data: Any = None
    ):
        super().__init__(
            message=message,
            error_code=ErrorCode.REDIS_ERROR,
            data=data,
            status_code=500
        )
        self.operation = operation
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert cache exception to dictionary."""
        result = super().to_dict()
        
        if self.operation:
            result["operation"] = self.operation
        
        return result


class NetworkException(BaseApplicationException):
    """
    Exception for network-related errors.
    
    Used when network operations fail.
    """
    
    def __init__(
        self,
        message: str = "Network error",
        url: str = None,
        data: Any = None
    ):
        super().__init__(
            message=message,
            error_code=ErrorCode.NETWORK_ERROR,
            data=data,
            status_code=503
        )
        self.url = url
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert network exception to dictionary."""
        result = super().to_dict()
        
        if self.url:
            result["url"] = self.url
        
        return result


class FileException(BaseApplicationException):
    """
    Exception for file operation errors.
    
    Used when file operations fail.
    """
    
    def __init__(
        self,
        message: str = "File operation error",
        file_path: str = None,
        operation: str = None,
        data: Any = None
    ):
        super().__init__(
            message=message,
            error_code=ErrorCode.FILE_ERROR,
            data=data,
            status_code=500
        )
        self.file_path = file_path
        self.operation = operation
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert file exception to dictionary."""
        result = super().to_dict()
        
        if self.file_path:
            result["file_path"] = self.file_path
        
        if self.operation:
            result["operation"] = self.operation
        
        return result
