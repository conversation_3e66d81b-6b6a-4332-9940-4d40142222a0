#!/usr/bin/env python3
"""
架构优化验证测试脚本。

这个脚本用于验证新架构的各个组件是否正常工作。
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


async def test_config_system():
    """测试配置系统"""
    print("🔧 测试配置系统...")
    
    try:
        # 测试新配置系统
        from core.config import get_app_config, get_database_config, get_redis_config
        
        app_config = get_app_config()
        db_config = get_database_config()
        redis_config = get_redis_config()
        
        print(f"  ✅ 应用配置: {app_config.app_name} v{app_config.app_version}")
        print(f"  ✅ 数据库配置: {db_config.db_type}://{db_config.db_host}:{db_config.db_port}")
        print(f"  ✅ Redis配置: {redis_config.redis_host}:{redis_config.redis_port}")
        
        # 测试配置工厂
        from core.config import ConfigFactory
        
        factory_config = ConfigFactory.get_app_config()
        print(f"  ✅ 配置工厂: {factory_config.app_name}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 配置系统测试失败: {e}")
        return False


async def test_dependency_injection():
    """测试依赖注入系统"""
    print("🔌 测试依赖注入系统...")
    
    try:
        from core.container import get_container, DIContainer
        
        # 获取容器
        container = get_container()
        print(f"  ✅ DI容器创建成功: {type(container).__name__}")
        
        # 测试服务注册
        class TestService:
            def __init__(self):
                self.name = "TestService"
        
        container.register_singleton(TestService)
        
        # 测试服务解析
        service = container.resolve(TestService)
        print(f"  ✅ 服务解析成功: {service.name}")
        
        # 测试单例模式
        service2 = container.resolve(TestService)
        is_singleton = service is service2
        print(f"  ✅ 单例模式验证: {is_singleton}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 依赖注入测试失败: {e}")
        return False


async def test_database_system():
    """测试数据库系统"""
    print("🗄️ 测试数据库系统...")
    
    try:
        from core.database import get_database_manager, DatabaseConnection
        
        # 测试数据库连接
        connection = DatabaseConnection()
        connection_info = connection.get_connection_info()
        print(f"  ✅ 数据库连接配置: {connection_info['db_type']}")
        
        # 测试数据库管理器
        manager = get_database_manager()
        print(f"  ✅ 数据库管理器创建成功: {type(manager).__name__}")
        
        # 测试健康检查（不实际连接数据库）
        print("  ✅ 数据库系统组件正常")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 数据库系统测试失败: {e}")
        return False


async def test_cache_system():
    """测试缓存系统"""
    print("🚀 测试缓存系统...")
    
    try:
        from core.cache import get_redis_manager
        
        # 测试Redis管理器
        redis_manager = get_redis_manager()
        print(f"  ✅ Redis管理器创建成功: {type(redis_manager).__name__}")
        
        # 测试序列化功能
        test_data = {"key": "value", "number": 123}
        serialized = redis_manager._serialize(test_data)
        deserialized = redis_manager._deserialize(serialized)
        
        is_equal = test_data == deserialized
        print(f"  ✅ 序列化/反序列化测试: {is_equal}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 缓存系统测试失败: {e}")
        return False


async def test_exception_system():
    """测试异常处理系统"""
    print("⚠️ 测试异常处理系统...")
    
    try:
        from core.exceptions import (
            BusinessException, 
            AuthenticationException,
            ResourceNotFoundException,
            ErrorCode
        )
        
        # 测试业务异常
        try:
            raise BusinessException("测试业务异常")
        except BusinessException as e:
            exception_dict = e.to_dict()
            print(f"  ✅ 业务异常: {exception_dict['message']}")
        
        # 测试认证异常
        try:
            raise AuthenticationException("测试认证异常", username="testuser")
        except AuthenticationException as e:
            exception_dict = e.to_dict()
            print(f"  ✅ 认证异常: {exception_dict['message']}")
        
        # 测试资源未找到异常
        try:
            raise ResourceNotFoundException(
                "资源未找到", 
                resource_type="User", 
                resource_id="123"
            )
        except ResourceNotFoundException as e:
            exception_dict = e.to_dict()
            print(f"  ✅ 资源异常: {exception_dict['message']}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 异常系统测试失败: {e}")
        return False


async def test_constants():
    """测试常量系统"""
    print("📋 测试常量系统...")
    
    try:
        from core.config.constants import (
            CommonConstants,
            HttpStatusConstants,
            ErrorCode,
            BusinessType
        )
        
        # 测试常量访问
        print(f"  ✅ HTTP状态码: {HttpStatusConstants.SUCCESS}")
        print(f"  ✅ 通用常量: {CommonConstants.YES}")
        print(f"  ✅ 业务类型: {BusinessType.INSERT.description}")
        print(f"  ✅ 错误码: {ErrorCode.USER_NOT_FOUND.message}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 常量系统测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始架构优化验证测试...\n")
    
    tests = [
        ("配置系统", test_config_system),
        ("依赖注入", test_dependency_injection),
        ("数据库系统", test_database_system),
        ("缓存系统", test_cache_system),
        ("异常处理", test_exception_system),
        ("常量系统", test_constants),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"  ❌ {test_name}测试出现异常: {e}")
            results.append((test_name, False))
        
        print()  # 空行分隔
    
    # 输出测试结果
    print("=" * 50)
    print("📊 测试结果汇总:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:12} : {status}")
        if result:
            passed += 1
    
    print("-" * 50)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！架构优化成功！")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查相关组件。")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
