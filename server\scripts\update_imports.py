#!/usr/bin/env python3
"""
批量更新导入引用的脚本。

这个脚本用于将旧的配置和核心模块引用更新为新的架构。
"""

import os
import re
from pathlib import Path
from typing import Dict, List, Tuple


class ImportUpdater:
    """导入更新器"""
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.replacements = self._get_replacements()
    
    def _get_replacements(self) -> List[Tuple[str, str]]:
        """获取需要替换的导入映射"""
        return [
            # 配置相关替换
            ("from core.config import get_app_config
AppConfig = get_app_config()", "from core.config import get_app_config\nAppConfig = get_app_config()"),
            ("from core.config import get_database_config
DataBaseConfig = get_database_config()", "from core.config import get_database_config\nDataBaseConfig = get_database_config()"),
            ("from core.config import get_redis_config
RedisConfig = get_redis_config()", "from core.config import get_redis_config\nRedisConfig = get_redis_config()"),
            ("from core.config import get_jwt_config
JwtConfig = get_jwt_config()", "from core.config import get_jwt_config\nJwtConfig = get_jwt_config()"),
            ("from core.config import get_gen_config
GenConfig = get_gen_config()", "from core.config import get_gen_config\nGenConfig = get_gen_config()"),
            ("from core.config import get_upload_config
UploadConfig = get_upload_config()", "from core.config import get_upload_config\nUploadConfig = get_upload_config()"),
            
            # 常量相关替换
            ("from core.config.constants import CommonConstants as CommonConstant", "from core.config.constants import CommonConstants as CommonConstant"),
            ("from core.config.constants import HttpStatusConstants as HttpStatusConstant", "from core.config.constants import HttpStatusConstants as HttpStatusConstant"),
            ("from core.config.constants import JobConstants as JobConstant", "from core.config.constants import JobConstants as JobConstant"),
            ("from core.config.constants import MenuConstants as MenuConstant", "from core.config.constants import MenuConstants as MenuConstant"),
            ("from core.config.constants import GenConstants as GenConstant", "from core.config.constants import GenConstants as GenConstant"),
            
            # 枚举相关替换
            ("from core.config.constants import BusinessType", "from core.config.constants import BusinessType"),
            ("from core.config.constants import RedisKeyConfig as RedisInitKeyConfig", "from core.config.constants import RedisKeyConfig as RedisInitKeyConfig"),
            
            # 数据库相关替换
            ("from core.database import get_database_session as get_db", "from core.database import get_database_session as get_db"),
            ("from core.database import DatabaseBase as Base", "from core.database import DatabaseBase as Base"),
            ("from core.database import init_create_table", "from core.database import init_create_table"),
            
            # 缓存相关替换
            ("from core.cache import RedisManager", "from core.cache import RedisManager"),
            
            # 异常相关替换
            ("from core.exceptions.business import", "from core.exceptions.business import"),
            
            # 日志相关替换
            ("from core.log import logger", "from core.log import logger"),
        ]
    
    def update_file(self, file_path: Path) -> bool:
        """更新单个文件的导入"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 应用所有替换
            for old_import, new_import in self.replacements:
                content = content.replace(old_import, new_import)
            
            # 如果内容有变化，写回文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"Updated: {file_path}")
                return True
            
            return False
            
        except Exception as e:
            print(f"Error updating {file_path}: {e}")
            return False
    
    def update_directory(self, directory: Path, extensions: List[str] = None) -> Dict[str, int]:
        """更新目录中的所有文件"""
        if extensions is None:
            extensions = ['.py']
        
        stats = {'updated': 0, 'total': 0, 'errors': 0}
        
        for file_path in directory.rglob('*'):
            if file_path.is_file() and file_path.suffix in extensions:
                # 跳过某些目录
                if any(part in str(file_path) for part in ['__pycache__', '.git', 'node_modules', 'venv']):
                    continue
                
                stats['total'] += 1
                
                try:
                    if self.update_file(file_path):
                        stats['updated'] += 1
                except Exception as e:
                    print(f"Error processing {file_path}: {e}")
                    stats['errors'] += 1
        
        return stats
    
    def update_project(self) -> Dict[str, int]:
        """更新整个项目"""
        print(f"Updating imports in project: {self.project_root}")
        
        # 更新server目录
        server_dir = self.project_root / "server"
        if server_dir.exists():
            return self.update_directory(server_dir)
        else:
            print(f"Server directory not found: {server_dir}")
            return {'updated': 0, 'total': 0, 'errors': 1}


def main():
    """主函数"""
    # 获取项目根目录
    script_dir = Path(__file__).parent
    project_root = script_dir.parent.parent
    
    # 创建更新器
    updater = ImportUpdater(str(project_root))
    
    # 更新项目
    stats = updater.update_project()
    
    # 打印统计信息
    print("\n" + "="*50)
    print("Import Update Summary:")
    print(f"Total files processed: {stats['total']}")
    print(f"Files updated: {stats['updated']}")
    print(f"Errors: {stats['errors']}")
    print("="*50)


if __name__ == "__main__":
    main()
