"""
Decorators for dependency injection.

This module provides decorators to simplify service registration
and dependency injection in the application.
"""

import functools
from typing import Any, Callable, Type, TypeVar
from .container import get_container
from .providers import LifecycleType

T = TypeVar('T')


def injectable(
    interface: Type[T] = None,
    lifecycle: LifecycleType = LifecycleType.TRANSIENT
) -> Callable[[Type[T]], Type[T]]:
    """
    Decorator to mark a class as injectable service.
    
    Args:
        interface: Service interface (defaults to the decorated class)
        lifecycle: Service lifecycle type
        
    Returns:
        Decorated class
    """
    def decorator(cls: Type[T]) -> Type[T]:
        container = get_container()
        service_interface = interface or cls
        
        if lifecycle == LifecycleType.SINGLETON:
            container.register_singleton(service_interface, cls)
        elif lifecycle == LifecycleType.SCOPED:
            container.register_scoped(service_interface, cls)
        else:
            container.register_transient(service_interface, cls)
        
        return cls
    
    return decorator


def singleton(interface: Type[T] = None) -> Callable[[Type[T]], Type[T]]:
    """
    Decorator to register a class as singleton service.
    
    Args:
        interface: Service interface (defaults to the decorated class)
        
    Returns:
        Decorated class
    """
    return injectable(interface, LifecycleType.SINGLETON)


def scoped(interface: Type[T] = None) -> Callable[[Type[T]], Type[T]]:
    """
    Decorator to register a class as scoped service.
    
    Args:
        interface: Service interface (defaults to the decorated class)
        
    Returns:
        Decorated class
    """
    return injectable(interface, LifecycleType.SCOPED)


def transient(interface: Type[T] = None) -> Callable[[Type[T]], Type[T]]:
    """
    Decorator to register a class as transient service.
    
    Args:
        interface: Service interface (defaults to the decorated class)
        
    Returns:
        Decorated class
    """
    return injectable(interface, LifecycleType.TRANSIENT)


def inject(func: Callable) -> Callable:
    """
    Decorator to inject dependencies into function parameters.
    
    Args:
        func: Function to inject dependencies into
        
    Returns:
        Decorated function with dependency injection
    """
    import inspect
    from typing import get_type_hints
    
    signature = inspect.signature(func)
    type_hints = get_type_hints(func)
    
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        container = get_container()
        
        # Get parameter names that need injection
        param_names = list(signature.parameters.keys())
        
        # Skip parameters that are already provided
        provided_params = len(args)
        injection_params = param_names[provided_params:]
        
        # Inject dependencies for remaining parameters
        for param_name in injection_params:
            if param_name in kwargs:
                continue  # Already provided
            
            if param_name in type_hints:
                param_type = type_hints[param_name]
                if container.is_registered(param_type):
                    kwargs[param_name] = container.resolve(param_type)
        
        return func(*args, **kwargs)
    
    return wrapper


def factory(
    interface: Type[T],
    lifecycle: LifecycleType = LifecycleType.SINGLETON
) -> Callable[[Callable[[], T]], Callable[[], T]]:
    """
    Decorator to register a function as service factory.
    
    Args:
        interface: Service interface
        lifecycle: Service lifecycle type
        
    Returns:
        Decorated factory function
    """
    def decorator(factory_func: Callable[[], T]) -> Callable[[], T]:
        container = get_container()
        container.register_factory(interface, factory_func)
        return factory_func
    
    return decorator


class DependencyInjector:
    """Helper class for manual dependency injection."""
    
    def __init__(self, container=None):
        self.container = container or get_container()
    
    def resolve(self, interface: Type[T]) -> T:
        """Resolve service instance."""
        return self.container.resolve(interface)
    
    def inject_into(self, func: Callable, *args, **kwargs) -> Any:
        """Inject dependencies into function call."""
        import inspect
        from typing import get_type_hints
        
        signature = inspect.signature(func)
        type_hints = get_type_hints(func)
        
        # Get parameter names that need injection
        param_names = list(signature.parameters.keys())
        
        # Skip parameters that are already provided
        provided_params = len(args)
        injection_params = param_names[provided_params:]
        
        # Inject dependencies for remaining parameters
        for param_name in injection_params:
            if param_name in kwargs:
                continue  # Already provided
            
            if param_name in type_hints:
                param_type = type_hints[param_name]
                if self.container.is_registered(param_type):
                    kwargs[param_name] = self.container.resolve(param_type)
        
        return func(*args, **kwargs)


# Global injector instance
injector = DependencyInjector()


def auto_inject(cls: Type[T]) -> Type[T]:
    """
    Class decorator to automatically inject dependencies into all methods.
    
    Args:
        cls: Class to decorate
        
    Returns:
        Decorated class with auto-injection
    """
    import inspect
    
    # Get all methods of the class
    for name, method in inspect.getmembers(cls, predicate=inspect.isfunction):
        if name.startswith('_'):
            continue  # Skip private methods
        
        # Apply injection decorator
        setattr(cls, name, inject(method))
    
    return cls
