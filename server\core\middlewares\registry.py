"""
Middleware registry for managing middleware registration and discovery.

This module provides a centralized registry for middleware management,
including registration, discovery, and lifecycle management.
"""

from typing import Dict, List, Optional, Type, Any
from collections import defaultdict
import asyncio
from fastapi import FastAPI
from core.container import singleton
from core.log import logger
from .base import BaseMiddleware, MiddlewareConfig, MiddlewareMetricsCollector


@singleton()
class MiddlewareRegistry:
    """
    Central registry for middleware management.
    
    Handles middleware registration, discovery, and lifecycle management.
    """
    
    def __init__(self):
        self._middlewares: Dict[str, BaseMiddleware] = {}
        self._middleware_order: List[str] = []
        self._enabled_middlewares: Dict[str, bool] = {}
        self._middleware_dependencies: Dict[str, List[str]] = defaultdict(list)
        self._metrics_collector = MiddlewareMetricsCollector()
    
    def register(
        self, 
        middleware: BaseMiddleware, 
        dependencies: List[str] = None
    ) -> None:
        """
        Register a middleware instance.
        
        Args:
            middleware: Middleware instance to register
            dependencies: List of middleware names this middleware depends on
        """
        name = middleware.get_name()
        
        if name in self._middlewares:
            logger.warning(f"Middleware '{name}' is already registered, replacing...")
        
        # Validate configuration
        if not middleware.validate_config():
            raise ValueError(f"Invalid configuration for middleware '{name}'")
        
        self._middlewares[name] = middleware
        self._enabled_middlewares[name] = middleware.is_enabled()
        
        # Handle dependencies
        if dependencies:
            self._middleware_dependencies[name] = dependencies
        
        # Update order based on priority
        self._update_middleware_order()
        
        logger.info(f"Registered middleware: {middleware}")
    
    def unregister(self, name: str) -> bool:
        """
        Unregister a middleware.
        
        Args:
            name: Name of the middleware to unregister
            
        Returns:
            True if middleware was unregistered, False if not found
        """
        if name not in self._middlewares:
            return False
        
        # Check if other middlewares depend on this one
        dependents = self._get_dependents(name)
        if dependents:
            raise ValueError(
                f"Cannot unregister middleware '{name}' because it has dependents: {dependents}"
            )
        
        middleware = self._middlewares[name]
        
        # Cleanup middleware
        asyncio.create_task(middleware.cleanup())
        
        # Remove from registry
        del self._middlewares[name]
        del self._enabled_middlewares[name]
        
        if name in self._middleware_dependencies:
            del self._middleware_dependencies[name]
        
        # Remove from order
        if name in self._middleware_order:
            self._middleware_order.remove(name)
        
        logger.info(f"Unregistered middleware: {name}")
        return True
    
    def get_middleware(self, name: str) -> Optional[BaseMiddleware]:
        """Get a middleware by name."""
        return self._middlewares.get(name)
    
    def get_all_middlewares(self) -> Dict[str, BaseMiddleware]:
        """Get all registered middlewares."""
        return self._middlewares.copy()
    
    def get_enabled_middlewares(self) -> Dict[str, BaseMiddleware]:
        """Get all enabled middlewares."""
        return {
            name: middleware 
            for name, middleware in self._middlewares.items()
            if self._enabled_middlewares.get(name, False)
        }
    
    def get_middleware_order(self) -> List[str]:
        """Get the middleware execution order."""
        return [
            name for name in self._middleware_order 
            if self._enabled_middlewares.get(name, False)
        ]
    
    def enable_middleware(self, name: str) -> bool:
        """Enable a middleware."""
        if name not in self._middlewares:
            return False
        
        self._enabled_middlewares[name] = True
        self._middlewares[name].update_config(enabled=True)
        logger.info(f"Enabled middleware: {name}")
        return True
    
    def disable_middleware(self, name: str) -> bool:
        """Disable a middleware."""
        if name not in self._middlewares:
            return False
        
        self._enabled_middlewares[name] = False
        self._middlewares[name].update_config(enabled=False)
        logger.info(f"Disabled middleware: {name}")
        return True
    
    def is_registered(self, name: str) -> bool:
        """Check if a middleware is registered."""
        return name in self._middlewares
    
    def is_enabled(self, name: str) -> bool:
        """Check if a middleware is enabled."""
        return self._enabled_middlewares.get(name, False)
    
    async def setup_all(self, app: FastAPI) -> None:
        """Setup all enabled middlewares on the FastAPI application."""
        enabled_middlewares = self.get_enabled_middlewares()
        middleware_order = self.get_middleware_order()
        
        logger.info(f"Setting up {len(enabled_middlewares)} middlewares...")
        
        # Setup middlewares in dependency order
        setup_order = self._resolve_dependencies(middleware_order)
        
        for name in setup_order:
            if name in enabled_middlewares:
                middleware = enabled_middlewares[name]
                
                try:
                    # Initialize middleware
                    await middleware.initialize()
                    
                    # Setup middleware on app
                    import time
                    start_time = time.time()
                    
                    await middleware.setup(app)
                    
                    setup_time = time.time() - start_time
                    self._metrics_collector.get_metrics().record_setup_time(name, setup_time)
                    
                    logger.info(f"Setup middleware '{name}' in {setup_time:.3f}s")
                    
                except Exception as e:
                    logger.error(f"Failed to setup middleware '{name}': {e}")
                    raise
        
        logger.info("All middlewares setup completed")
    
    def get_middleware_info(self) -> Dict[str, Any]:
        """Get information about all registered middlewares."""
        info = {}
        
        for name, middleware in self._middlewares.items():
            info[name] = {
                "name": middleware.get_name(),
                "description": middleware.get_description(),
                "enabled": self.is_enabled(name),
                "priority": middleware.get_priority(),
                "config": middleware.get_config_dict(),
                "dependencies": self._middleware_dependencies.get(name, [])
            }
        
        return info
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get middleware metrics."""
        return self._metrics_collector.get_metrics().get_metrics()
    
    def _update_middleware_order(self) -> None:
        """Update middleware order based on priority."""
        # Sort by priority (lower number = higher priority)
        sorted_middlewares = sorted(
            self._middlewares.items(),
            key=lambda x: x[1].get_priority()
        )
        
        self._middleware_order = [name for name, _ in sorted_middlewares]
    
    def _get_dependents(self, middleware_name: str) -> List[str]:
        """Get middlewares that depend on the given middleware."""
        dependents = []
        
        for name, dependencies in self._middleware_dependencies.items():
            if middleware_name in dependencies:
                dependents.append(name)
        
        return dependents
    
    def _resolve_dependencies(self, middleware_names: List[str]) -> List[str]:
        """Resolve middleware dependencies and return setup order."""
        resolved = []
        visited = set()
        visiting = set()
        
        def visit(name: str):
            if name in visiting:
                raise ValueError(f"Circular dependency detected involving middleware '{name}'")
            
            if name in visited:
                return
            
            visiting.add(name)
            
            # Visit dependencies first
            dependencies = self._middleware_dependencies.get(name, [])
            for dep in dependencies:
                if dep not in middleware_names:
                    logger.warning(f"Dependency '{dep}' for middleware '{name}' is not available")
                    continue
                visit(dep)
            
            visiting.remove(name)
            visited.add(name)
            resolved.append(name)
        
        for name in middleware_names:
            visit(name)
        
        return resolved
    
    def clear(self) -> None:
        """Clear all registered middlewares."""
        self._middlewares.clear()
        self._middleware_order.clear()
        self._enabled_middlewares.clear()
        self._middleware_dependencies.clear()
        logger.info("Cleared all middlewares from registry")
