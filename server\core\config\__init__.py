"""
Core configuration module for the FastAPI application.

This module provides a unified configuration management system with:
- Type-safe configuration using Pydantic
- Environment variable auto-loading
- Configuration validation
- Factory pattern for different environments
"""

from .settings import (
    AppSettings,
    DatabaseSettings,
    RedisSettings,
    JwtSettings,
    GenSettings,
    UploadSettings,
    get_settings
)
from .factory import (
    get_app_config,
    get_database_config,
    get_redis_config,
    get_jwt_config,
    get_gen_config,
    get_upload_config
)
from .constants import (
    CommonConstants,
    HttpStatusConstants,
    JobConstants,
    MenuConstants,
    GenConstants
)
from .factory import ConfigFactory

__all__ = [
    "AppSettings",
    "DatabaseSettings",
    "RedisSettings",
    "JwtSettings",
    "GenSettings",
    "UploadSettings",
    "get_settings",
    "get_app_config",
    "get_database_config",
    "get_redis_config",
    "get_jwt_config",
    "get_gen_config",
    "get_upload_config",
    "CommonConstants",
    "HttpStatusConstants",
    "JobConstants",
    "MenuConstants",
    "GenConstants",
    "ConfigFactory"
]
