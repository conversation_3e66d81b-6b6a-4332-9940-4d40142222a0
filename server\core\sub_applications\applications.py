"""
Concrete sub-application implementations.

This module provides implementations of common sub-applications
using the new sub-application architecture.
"""

import os
from typing import Any, Dict, List
from fastapi import FastAP<PERSON>
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
from pydantic import Field
from core.config import get_upload_config, get_app_config
from .base import BaseSubApplication, SubApplicationConfig, sub_application_decorator


class StaticFilesConfig(SubApplicationConfig):
    """Configuration for static files sub-application."""
    
    directory: str = Field(description="Directory path for static files")
    html: bool = Field(default=False, description="Enable HTML file serving")
    check_dir: bool = Field(default=True, description="Check if directory exists")


@sub_application_decorator("static_files")
class StaticFilesApplication(BaseSubApplication):
    """Static files serving sub-application."""
    
    def __init__(self, config: StaticFilesConfig = None):
        if config is None:
            upload_config = get_upload_config()
            config = StaticFilesConfig(
                name="static_files",
                mount_path=upload_config.upload_prefix,
                directory=upload_config.upload_path,
                priority=10
            )
        super().__init__(config)
    
    def get_name(self) -> str:
        return "StaticFiles"
    
    def get_description(self) -> str:
        return "Static file serving sub-application for uploads and assets"
    
    async def _initialize(self) -> None:
        """Initialize static files directory."""
        directory = self.config.directory
        
        # Create directory if it doesn't exist
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
            from core.log import logger
            logger.info(f"Created static files directory: {directory}")
    
    async def create_application(self) -> StaticFiles:
        """Create StaticFiles application."""
        return StaticFiles(
            directory=self.config.directory,
            html=self.config.html,
            check_dir=self.config.check_dir
        )


class HealthCheckConfig(SubApplicationConfig):
    """Configuration for health check sub-application."""
    
    include_details: bool = Field(default=True, description="Include detailed health information")
    check_database: bool = Field(default=True, description="Include database health check")
    check_cache: bool = Field(default=True, description="Include cache health check")


@sub_application_decorator("health_check")
class HealthCheckApplication(BaseSubApplication):
    """Health check sub-application."""
    
    def __init__(self, config: HealthCheckConfig = None):
        if config is None:
            config = HealthCheckConfig(
                name="health_check",
                mount_path="/health",
                priority=5
            )
        super().__init__(config)
    
    def get_name(self) -> str:
        return "HealthCheck"
    
    def get_description(self) -> str:
        return "Health check endpoints for monitoring application status"
    
    async def create_application(self) -> FastAPI:
        """Create health check FastAPI application."""
        health_app = FastAPI(
            title="Health Check API",
            description="Health monitoring endpoints",
            docs_url="/docs" if self.config.include_details else None,
            redoc_url="/redoc" if self.config.include_details else None
        )
        
        @health_app.get("/")
        async def health_check():
            """Basic health check endpoint."""
            return {"status": "healthy", "service": "ruoyi-fastapi"}
        
        @health_app.get("/detailed")
        async def detailed_health_check():
            """Detailed health check with component status."""
            if not self.config.include_details:
                return {"error": "Detailed health check is disabled"}
            
            health_status = {
                "status": "healthy",
                "service": "ruoyi-fastapi",
                "components": {}
            }
            
            # Check database if enabled
            if self.config.check_database:
                try:
                    from core.database import get_database_manager
                    db_manager = get_database_manager()
                    db_health = await db_manager.health_check()
                    health_status["components"]["database"] = db_health
                except Exception as e:
                    health_status["components"]["database"] = {
                        "status": "unhealthy",
                        "error": str(e)
                    }
                    health_status["status"] = "degraded"
            
            # Check cache if enabled
            if self.config.check_cache:
                try:
                    from core.cache import get_redis_manager
                    redis_manager = get_redis_manager()
                    cache_health = await redis_manager.health_check()
                    health_status["components"]["cache"] = cache_health
                except Exception as e:
                    health_status["components"]["cache"] = {
                        "status": "unhealthy",
                        "error": str(e)
                    }
                    health_status["status"] = "degraded"
            
            return health_status
        
        return health_app


class AdminPanelConfig(SubApplicationConfig):
    """Configuration for admin panel sub-application."""
    
    admin_path: str = Field(default="/admin", description="Admin panel mount path")
    require_auth: bool = Field(default=True, description="Require authentication")
    allowed_users: List[str] = Field(default=[], description="List of allowed admin users")


@sub_application_decorator("admin_panel")
class AdminPanelApplication(BaseSubApplication):
    """Admin panel sub-application for system management."""
    
    def __init__(self, config: AdminPanelConfig = None):
        if config is None:
            config = AdminPanelConfig(
                name="admin_panel",
                mount_path="/admin",
                priority=20
            )
        super().__init__(config)
    
    def get_name(self) -> str:
        return "AdminPanel"
    
    def get_description(self) -> str:
        return "Administrative panel for system management and monitoring"
    
    async def create_application(self) -> FastAPI:
        """Create admin panel FastAPI application."""
        admin_app = FastAPI(
            title="Admin Panel",
            description="Administrative interface for system management",
            docs_url="/docs",
            redoc_url="/redoc"
        )
        
        @admin_app.get("/")
        async def admin_dashboard():
            """Admin dashboard endpoint."""
            return {
                "title": "RuoYi FastAPI Admin Panel",
                "version": get_app_config().app_version,
                "status": "active"
            }
        
        @admin_app.get("/system/info")
        async def system_info():
            """System information endpoint."""
            import psutil
            import platform
            
            return {
                "system": {
                    "platform": platform.platform(),
                    "python_version": platform.python_version(),
                    "cpu_count": psutil.cpu_count(),
                    "memory_total": psutil.virtual_memory().total,
                    "memory_available": psutil.virtual_memory().available
                },
                "application": {
                    "name": get_app_config().app_name,
                    "version": get_app_config().app_version,
                    "environment": get_app_config().app_env
                }
            }
        
        return admin_app


class APIDocumentationConfig(SubApplicationConfig):
    """Configuration for API documentation sub-application."""
    
    include_schemas: bool = Field(default=True, description="Include API schemas")
    include_examples: bool = Field(default=True, description="Include API examples")


@sub_application_decorator("api_docs")
class APIDocumentationApplication(BaseSubApplication):
    """API documentation sub-application."""
    
    def __init__(self, config: APIDocumentationConfig = None):
        if config is None:
            config = APIDocumentationConfig(
                name="api_docs",
                mount_path="/api-docs",
                priority=30
            )
        super().__init__(config)
    
    def get_name(self) -> str:
        return "APIDocumentation"
    
    def get_description(self) -> str:
        return "API documentation and interactive API explorer"
    
    async def create_application(self) -> FastAPI:
        """Create API documentation FastAPI application."""
        docs_app = FastAPI(
            title="API Documentation",
            description="Comprehensive API documentation and examples",
            docs_url="/swagger",
            redoc_url="/redoc"
        )
        
        @docs_app.get("/")
        async def api_docs_home():
            """API documentation home page."""
            return {
                "title": "RuoYi FastAPI Documentation",
                "description": "Comprehensive API documentation",
                "endpoints": {
                    "swagger": "/api-docs/swagger",
                    "redoc": "/api-docs/redoc",
                    "openapi": "/api-docs/openapi.json"
                }
            }
        
        return docs_app


class MetricsConfig(SubApplicationConfig):
    """Configuration for metrics sub-application."""
    
    include_system_metrics: bool = Field(default=True, description="Include system metrics")
    include_app_metrics: bool = Field(default=True, description="Include application metrics")


@sub_application_decorator("metrics")
class MetricsApplication(BaseSubApplication):
    """Metrics collection and monitoring sub-application."""
    
    def __init__(self, config: MetricsConfig = None):
        if config is None:
            config = MetricsConfig(
                name="metrics",
                mount_path="/metrics",
                priority=40
            )
        super().__init__(config)
    
    def get_name(self) -> str:
        return "Metrics"
    
    def get_description(self) -> str:
        return "Application and system metrics collection"
    
    async def create_application(self) -> FastAPI:
        """Create metrics FastAPI application."""
        metrics_app = FastAPI(
            title="Metrics API",
            description="Application and system metrics",
            docs_url="/docs",
            redoc_url="/redoc"
        )
        
        @metrics_app.get("/")
        async def metrics_summary():
            """Metrics summary endpoint."""
            return {
                "service": "ruoyi-fastapi",
                "metrics_available": [
                    "/metrics/system",
                    "/metrics/application",
                    "/metrics/middleware",
                    "/metrics/sub-applications"
                ]
            }
        
        @metrics_app.get("/system")
        async def system_metrics():
            """System metrics endpoint."""
            if not self.config.include_system_metrics:
                return {"error": "System metrics are disabled"}
            
            import psutil
            
            return {
                "cpu": {
                    "usage_percent": psutil.cpu_percent(interval=1),
                    "count": psutil.cpu_count()
                },
                "memory": {
                    "total": psutil.virtual_memory().total,
                    "available": psutil.virtual_memory().available,
                    "percent": psutil.virtual_memory().percent
                },
                "disk": {
                    "usage": psutil.disk_usage('/').percent,
                    "free": psutil.disk_usage('/').free
                }
            }
        
        @metrics_app.get("/application")
        async def application_metrics():
            """Application metrics endpoint."""
            if not self.config.include_app_metrics:
                return {"error": "Application metrics are disabled"}
            
            # Get middleware metrics
            try:
                from core.middlewares import get_middleware_manager
                middleware_manager = get_middleware_manager()
                middleware_metrics = middleware_manager.registry.get_metrics()
            except Exception:
                middleware_metrics = {}
            
            # Get sub-application metrics
            try:
                from core.sub_applications import get_sub_application_manager
                sub_app_manager = get_sub_application_manager()
                sub_app_metrics = sub_app_manager.registry.get_metrics()
            except Exception:
                sub_app_metrics = {}
            
            return {
                "middleware": middleware_metrics,
                "sub_applications": sub_app_metrics
            }
        
        return metrics_app
