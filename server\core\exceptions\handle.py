"""
Legacy exception handler for backward compatibility.

This module provides backward compatibility with the old exception
handling system while using the new architecture underneath.
"""

from fastapi import FastAPI
from core.log import logger
from .handlers import setup_exception_handlers


def handle_exception(app: FastAPI):
    """
    全局异常处理 (Legacy function for backward compatibility)
    
    Args:
        app: FastAPI application instance
    """
    logger.info("Using legacy exception handler (consider migrating to new architecture)")
    
    # Setup exception handlers using the new system
    setup_exception_handlers(app)
