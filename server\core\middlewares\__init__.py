"""
Middleware management module for the FastAPI application.

This module provides:
- Unified middleware registration and configuration
- Middleware lifecycle management
- Configuration-driven middleware setup
- Extensible middleware architecture
"""

from .manager import MiddlewareManager, get_middleware_manager
from .base import BaseMiddleware, MiddlewareConfig
from .middlewares import (
    CORSMiddlewareConfig,
    GZipMiddlewareConfig,
    TraceMiddlewareConfig,
    SecurityMiddlewareConfig
)
from .registry import MiddlewareRegistry

__all__ = [
    "MiddlewareManager",
    "get_middleware_manager",
    "BaseMiddleware",
    "MiddlewareConfig",
    "CORSMiddlewareConfig",
    "GZipMiddlewareConfig",
    "TraceMiddlewareConfig",
    "SecurityMiddlewareConfig",
    "MiddlewareRegistry"
]