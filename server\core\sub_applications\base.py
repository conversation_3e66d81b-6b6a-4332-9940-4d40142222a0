"""
Base classes for sub-application management.

This module provides the foundation for the sub-application system,
including base classes and configuration interfaces.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, Union
from fastapi import FastAPI
from pydantic import BaseModel, Field
from core.container import singleton


class SubApplicationConfig(BaseModel):
    """Base configuration class for sub-applications."""
    
    enabled: bool = Field(default=True, description="Whether the sub-application is enabled")
    mount_path: str = Field(description="Path where the sub-application will be mounted")
    name: str = Field(description="Name identifier for the sub-application")
    priority: int = Field(default=100, description="Mount priority (lower = higher priority)")
    
    class Config:
        extra = "allow"  # Allow additional configuration fields


class BaseSubApplication(ABC):
    """
    Base class for all sub-application implementations.
    
    Provides a common interface for sub-application registration,
    configuration, and lifecycle management.
    """
    
    def __init__(self, config: SubApplicationConfig):
        self.config = config
        self._mounted = False
        self._initialized = False
    
    @abstractmethod
    def get_name(self) -> str:
        """Get the sub-application name."""
        pass
    
    @abstractmethod
    def get_description(self) -> str:
        """Get the sub-application description."""
        pass
    
    def get_mount_path(self) -> str:
        """Get the mount path for the sub-application."""
        return self.config.mount_path
    
    def get_priority(self) -> int:
        """Get the sub-application priority."""
        return self.config.priority
    
    def is_enabled(self) -> bool:
        """Check if the sub-application is enabled."""
        return self.config.enabled
    
    def is_mounted(self) -> bool:
        """Check if the sub-application is mounted."""
        return self._mounted
    
    @abstractmethod
    async def create_application(self) -> Union[FastAPI, Any]:
        """
        Create the sub-application instance.
        
        Returns:
            FastAPI application or other ASGI application
        """
        pass
    
    async def mount(self, main_app: FastAPI) -> None:
        """
        Mount the sub-application on the main FastAPI application.
        
        Args:
            main_app: Main FastAPI application instance
        """
        if self._mounted:
            return
        
        if not self.is_enabled():
            return
        
        # Initialize if not already done
        if not self._initialized:
            await self.initialize()
        
        # Create the sub-application
        sub_app = await self.create_application()
        
        # Mount the sub-application
        main_app.mount(
            self.get_mount_path(),
            sub_app,
            name=self.config.name
        )
        
        self._mounted = True
        await self._on_mounted(main_app)
    
    async def unmount(self, main_app: FastAPI) -> None:
        """
        Unmount the sub-application from the main FastAPI application.
        
        Args:
            main_app: Main FastAPI application instance
        """
        if not self._mounted:
            return
        
        # Note: FastAPI doesn't provide a direct unmount method
        # This is a placeholder for cleanup operations
        await self._on_unmounted(main_app)
        self._mounted = False
    
    async def initialize(self) -> None:
        """Initialize the sub-application (called once during startup)."""
        if not self._initialized:
            await self._initialize()
            self._initialized = True
    
    async def _initialize(self) -> None:
        """Override this method for custom initialization logic."""
        pass
    
    async def _on_mounted(self, main_app: FastAPI) -> None:
        """Called after the sub-application is mounted."""
        pass
    
    async def _on_unmounted(self, main_app: FastAPI) -> None:
        """Called after the sub-application is unmounted."""
        pass
    
    async def cleanup(self) -> None:
        """Cleanup resources when the sub-application is removed."""
        pass
    
    def validate_config(self) -> bool:
        """Validate the sub-application configuration."""
        try:
            self.config.model_validate(self.config.model_dump())
            return True
        except Exception:
            return False
    
    def get_config_dict(self) -> Dict[str, Any]:
        """Get configuration as dictionary."""
        return self.config.model_dump()
    
    def update_config(self, **kwargs) -> None:
        """Update sub-application configuration."""
        for key, value in kwargs.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get health status of the sub-application."""
        return {
            "name": self.get_name(),
            "enabled": self.is_enabled(),
            "mounted": self.is_mounted(),
            "initialized": self._initialized,
            "mount_path": self.get_mount_path(),
            "priority": self.get_priority()
        }
    
    def __str__(self) -> str:
        """String representation of the sub-application."""
        return f"{self.get_name()}(path={self.get_mount_path()}, enabled={self.is_enabled()})"
    
    def __repr__(self) -> str:
        """Detailed representation of the sub-application."""
        return (
            f"{self.__class__.__name__}("
            f"name='{self.get_name()}', "
            f"path='{self.get_mount_path()}', "
            f"enabled={self.is_enabled()}, "
            f"mounted={self.is_mounted()})"
        )


class SubApplicationFactory:
    """Factory for creating sub-application instances."""
    
    _sub_application_classes: Dict[str, type] = {}
    
    @classmethod
    def register_sub_application(cls, name: str, sub_app_class: type) -> None:
        """Register a sub-application class."""
        cls._sub_application_classes[name] = sub_app_class
    
    @classmethod
    def create_sub_application(cls, name: str, config: SubApplicationConfig) -> BaseSubApplication:
        """Create a sub-application instance by name."""
        if name not in cls._sub_application_classes:
            raise ValueError(f"Unknown sub-application: {name}")
        
        sub_app_class = cls._sub_application_classes[name]
        return sub_app_class(config)
    
    @classmethod
    def get_available_sub_applications(cls) -> Dict[str, type]:
        """Get all available sub-application classes."""
        return cls._sub_application_classes.copy()
    
    @classmethod
    def is_registered(cls, name: str) -> bool:
        """Check if a sub-application is registered."""
        return name in cls._sub_application_classes


def sub_application_decorator(name: str):
    """
    Decorator to register a sub-application class.
    
    Args:
        name: Sub-application name for registration
    """
    def decorator(cls: type) -> type:
        SubApplicationFactory.register_sub_application(name, cls)
        return cls
    
    return decorator


class SubApplicationMetrics:
    """Metrics collection for sub-application performance."""
    
    def __init__(self):
        self._metrics: Dict[str, Dict[str, Any]] = {}
    
    def record_mount_time(self, sub_app_name: str, mount_time: float) -> None:
        """Record sub-application mount time."""
        if sub_app_name not in self._metrics:
            self._metrics[sub_app_name] = {}
        self._metrics[sub_app_name]['mount_time'] = mount_time
    
    def record_request_count(self, sub_app_name: str) -> None:
        """Record request count for sub-application."""
        if sub_app_name not in self._metrics:
            self._metrics[sub_app_name] = {}
        
        count = self._metrics[sub_app_name].get('request_count', 0)
        self._metrics[sub_app_name]['request_count'] = count + 1
    
    def get_metrics(self, sub_app_name: str = None) -> Dict[str, Any]:
        """Get metrics for a specific sub-application or all sub-applications."""
        if sub_app_name:
            return self._metrics.get(sub_app_name, {})
        return self._metrics.copy()
    
    def reset_metrics(self, sub_app_name: str = None) -> None:
        """Reset metrics for a specific sub-application or all sub-applications."""
        if sub_app_name:
            if sub_app_name in self._metrics:
                self._metrics[sub_app_name] = {}
        else:
            self._metrics.clear()


@singleton()
class SubApplicationMetricsCollector:
    """Global sub-application metrics collector."""
    
    def __init__(self):
        self.metrics = SubApplicationMetrics()
    
    def get_metrics(self) -> SubApplicationMetrics:
        """Get the metrics instance."""
        return self.metrics
