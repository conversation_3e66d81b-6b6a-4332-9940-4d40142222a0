"""
Database base classes and utilities.

This module provides base classes for database models and common
database utilities.
"""

from sqlalchemy.ext.asyncio import AsyncAttrs
from sqlalchemy.orm import DeclarativeBase, declared_attr
from sqlalchemy import Column, DateTime, String, func
from datetime import datetime
from typing import Any


class DatabaseBase(AsyncAttrs, DeclarativeBase):
    """
    Base class for all database models.
    
    Provides common functionality like:
    - Async attribute access
    - Common fields (created_at, updated_at, etc.)
    - Utility methods
    """
    
    # Generate table name automatically from class name
    @declared_attr
    def __tablename__(cls) -> str:
        # Convert CamelCase to snake_case
        import re
        name = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', cls.__name__)
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', name).lower()


class TimestampMixin:
    """Mixin for adding timestamp fields to models."""
    
    created_at = Column(
        DateTime,
        default=datetime.now,
        server_default=func.now(),
        nullable=False,
        comment="创建时间"
    )
    
    updated_at = Column(
        DateTime,
        default=datetime.now,
        onupdate=datetime.now,
        server_default=func.now(),
        server_onupdate=func.now(),
        nullable=False,
        comment="更新时间"
    )


class SoftDeleteMixin:
    """Mixin for adding soft delete functionality."""
    
    deleted_at = Column(
        DateTime,
        nullable=True,
        comment="删除时间"
    )
    
    is_deleted = Column(
        String(1),
        default='0',
        nullable=False,
        comment="删除标志（0代表存在 1代表删除）"
    )
    
    def soft_delete(self):
        """Mark record as deleted."""
        self.is_deleted = '1'
        self.deleted_at = datetime.now()
    
    def restore(self):
        """Restore deleted record."""
        self.is_deleted = '0'
        self.deleted_at = None


class AuditMixin:
    """Mixin for adding audit fields to models."""
    
    created_by = Column(
        String(50),
        nullable=True,
        comment="创建者"
    )
    
    updated_by = Column(
        String(50),
        nullable=True,
        comment="更新者"
    )


class BaseModel(DatabaseBase, TimestampMixin, SoftDeleteMixin, AuditMixin):
    """
    Complete base model with all common functionality.
    
    Includes:
    - Timestamp fields (created_at, updated_at)
    - Soft delete fields (deleted_at, is_deleted)
    - Audit fields (created_by, updated_by)
    """
    
    __abstract__ = True
    
    def to_dict(self, exclude_fields: set[str] = None) -> dict[str, Any]:
        """
        Convert model instance to dictionary.
        
        Args:
            exclude_fields: Fields to exclude from the result
            
        Returns:
            Dictionary representation of the model
        """
        exclude_fields = exclude_fields or set()
        exclude_fields.update({'_sa_instance_state'})
        
        result = {}
        for column in self.__table__.columns:
            field_name = column.name
            if field_name not in exclude_fields:
                value = getattr(self, field_name)
                # Convert datetime to string for JSON serialization
                if isinstance(value, datetime):
                    value = value.isoformat()
                result[field_name] = value
        
        return result
    
    def update_from_dict(self, data: dict[str, Any], exclude_fields: set[str] = None):
        """
        Update model instance from dictionary.
        
        Args:
            data: Dictionary with field values
            exclude_fields: Fields to exclude from update
        """
        exclude_fields = exclude_fields or set()
        exclude_fields.update({'id', 'created_at', 'created_by'})
        
        for key, value in data.items():
            if key not in exclude_fields and hasattr(self, key):
                setattr(self, key, value)
    
    def __repr__(self) -> str:
        """String representation of the model."""
        class_name = self.__class__.__name__
        primary_key = getattr(self, 'id', 'unknown')
        return f"<{class_name}(id={primary_key})>"


class ReadOnlyModel(DatabaseBase, TimestampMixin):
    """
    Base model for read-only entities (views, etc.).
    
    Includes only timestamp fields without audit or soft delete.
    """
    
    __abstract__ = True
    
    def to_dict(self, exclude_fields: set[str] = None) -> dict[str, Any]:
        """Convert model instance to dictionary."""
        exclude_fields = exclude_fields or set()
        exclude_fields.update({'_sa_instance_state'})
        
        result = {}
        for column in self.__table__.columns:
            field_name = column.name
            if field_name not in exclude_fields:
                value = getattr(self, field_name)
                if isinstance(value, datetime):
                    value = value.isoformat()
                result[field_name] = value
        
        return result
